var _0xdc108b=(106575^106568)+(975319^975315);const PREDEFINED_DATASETS={"default":"\u0036\u0035\u0020\u0030\u000A\u0036\u0033\u0020\u0031\u0036\u0035\u000A\u0036\u0031\u0020\u0031\u0036\u0039\u000A\u0035\u0039\u0020\u0031\u0036\u0034\u000A\u0035\u0037\u0020\u0031\u0036\u0033\u000A\u0035\u0035\u0020\u0031\u0036\u0030\u000A\u0035\u0033\u0020\u0031\u0037\u0037\u000A\u0035\u0031\u0020\u0031\u0038\u0039\u000A\u0034\u0039\u0020\u0032\u0032\u0038\u000A\u0034\u0037\u0020\u0032\u0033\u0037\u000A\u0034\u0035\u0020\u0032\u0038\u0030\u000A\u0034\u0033\u0020\u0033\u0035\u0035","\u0064\u0061\u0074\u0061\u0073\u0065\u0074\u0031":"50.28 166\n48.28 142\n46.28 154\n44.28 151\n42.28 149\n40.28 153\n38.28 167\n36.28 167\n34.28 161\n32.28 208\n30.28 266","\u0064\u0061\u0074\u0061\u0073\u0065\u0074\u0032":"\u0031\u0034\u0033\u002E\u0035\u0020\u0030\u000A\u0031\u0034\u0031\u002E\u0035\u0020\u0032\u0033\u0036\u000A\u0031\u0033\u0039\u002E\u0035\u0020\u0032\u0033\u0032\u000A\u0031\u0033\u0037\u002E\u0035\u0020\u0032\u0031\u0039\u000A\u0031\u0033\u0035\u002E\u0035\u0020\u0032\u0032\u0032\u000A\u0031\u0033\u0033\u002E\u0035\u0020\u0032\u0031\u0039\u000A\u0031\u0033\u0031\u002E\u0035\u0020\u0032\u0032\u0033\u000A\u0031\u0032\u0039\u002E\u0035\u0020\u0032\u0032\u0034\u000A\u0031\u0032\u0037\u002E\u0035\u0020\u0032\u0034\u0032\u000A\u0031\u0032\u0035\u002E\u0035\u0020\u0032\u0035\u0035\u000A\u0031\u0032\u0033\u002E\u0035\u0020\u0032\u0039\u0032\u000A\u0031\u0032\u0031\u002E\u0035\u0020\u0033\u0034\u0030"};_0xdc108b=956938^956943;var _0x4de=(420947^420944)+(742960^742965);let calculatedXData=null;_0x4de="bbodhd".split("").reverse().join("");var _0xf6f7a;let calculatedUData=null;_0xf6f7a=500372^500375;let currentModelResults={};function parseRawData(rawDataText){var _0x4649fa=(693019^693016)+(839752^839752);const _0x958a=rawDataText['\u0074\u0072\u0069\u006D']()['\u0073\u0070\u006C\u0069\u0074']("\u000A");_0x4649fa=(135315^135322)+(813191^813188);const _0x2beef=[];for(let i=336011^336011;i<_0x958a['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){const _0x2g3ccb=_0x958a[i]['\u0074\u0072\u0069\u006D']();if(_0x2g3ccb===''||_0x2g3ccb['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0023"))continue;var _0x3140ac;const _0xcec8e=_0x2g3ccb['\u0073\u0070\u006C\u0069\u0074'](new RegExp("+s\\".split("").reverse().join(""),""));_0x3140ac=545655^545649;if(_0xcec8e['\u006C\u0065\u006E\u0067\u0074\u0068']>=(509199^509197)){var _0xf9b5c;const _0x298fb=parseFloat(_0xcec8e[498685^498685]);_0xf9b5c=(312271^312266)+(641711^641710);var _0x98gbbc;const _0x2cffbe=parseFloat(_0xcec8e[934442^934443]);_0x98gbbc='\u006E\u006D\u006D\u0063\u006D\u006B';if(!isNaN(_0x298fb)&&!isNaN(_0x2cffbe)){_0x2beef['\u0070\u0075\u0073\u0068']([_0x298fb,_0x2cffbe]);}}}return _0x2beef;}function calculateXUFromRaw(rawDataText,gcG,sCm2,deltaWG){try{if(gcG<=(855125^855125)||sCm2<=(615252^615252)||deltaWG<=(290392^290392)){throw new Error("\u0047\u0063\u002C\u0020\u0053\u0028\u0063\u006D\u0032\u0029\u002C\u0020\u548C\u0020\u0394\u0057\u0020\u5FC5\u987B\u4E3A\u6B63\u6570");}const _0x8c7eg=gcG/(626113^626217);const _0x6c30fb=sCm2/(678570^687546);var _0xc4bd=(596145^596147)+(932026^932019);const _0x0f47f=parseRawData(rawDataText);_0xc4bd=840872^840864;if(_0x0f47f['\u006C\u0065\u006E\u0067\u0074\u0068']<(744434^744432)){throw new Error("\u81F3\u5C11\u9700\u8981\u4E24\u4E2A\u6709\u6548\u7684\u6570\u636E\u884C\u0020\u0028\u0047\u0069\u002C\u0020\u0064\u0065\u006C\u0074\u0061\u005F\u0074\u0061\u0075\u0029");}var _0x4e743c=(612851^612858)+(633375^633372);const _0xcfe2f=_0x0f47f['\u006D\u0061\u0070'](row=>row[657015^657015]);_0x4e743c=(747884^747883)+(192978^192977);const _0x01d=_0x0f47f['\u006D\u0061\u0070'](row=>row[590130^590131]);for(let i=334776^334776;i<_0xcfe2f['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){if(_0xcfe2f[i]<gcG-1e-9){throw new Error(`第 ${i+(724466^724467)} 行的 Gi 值小于 Gc (${gcG['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](398739^398737)}g)`);}}for(let i=369720^369720;i<_0x01d['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){if(_0x01d[i]<(587614^587614)){throw new Error(`第 ${i+(142948^142949)} 行发现负的 delta_tau 值`);}}var _0x_0xf1d=(162792^162794)+(716410^716413);const _0xc976bd=_0xcfe2f['\u006D\u0061\u0070'](gi=>(gi-gcG)/gcG);_0x_0xf1d=(278990^278983)+(192494^192488);const _0xae7bfc=[];var _0xe78cf=(546158^546156)+(367101^367101);const _0xfb_0x46a=[];_0xe78cf=353839^353832;var _0x18e1f;const _0x5758d=_0xc976bd['\u006C\u0065\u006E\u0067\u0074\u0068']-(466268^466269);_0x18e1f=(542097^542101)+(430806^430803);for(let i=193562^193562;i<_0x5758d;i++){const _0x9a5a8c=(_0xc976bd[i]+_0xc976bd[i+(902405^902404)])/(306230^306228);_0xae7bfc['\u0070\u0075\u0073\u0068'](_0x9a5a8c);const _0x06g=_0x01d[i+(780518^780519)];if(_0x06g<=1e-9){console['\u0077\u0061\u0072\u006E'](`间隔 ${i+(337372^337373)} 的 delta_tau 为零或接近零 (${_0x06g} s)，跳过 U 计算`);_0xfb_0x46a['\u0070\u0075\u0073\u0068'](NaN);continue;}var _0xe430ee=(361752^361755)+(162071^162070);const u=deltaWG/(306035^305307)/(_0x6c30fb*_0x06g);_0xe430ee=(802434^802438)+(482957^482948);_0xfb_0x46a['\u0070\u0075\u0073\u0068'](u);const _0x9ff99f=_0xcfe2f[i]-_0xcfe2f[i+(116987^116986)];if(Math['\u0061\u0062\u0073'](_0x9ff99f-deltaWG)>deltaWG*0.20){console['\u0077\u0061\u0072\u006E'](`间隔 ${i+(590503^590502)} 质量下降不一致。期望 ≈${deltaWG}g，实际 ${_0x9ff99f['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](716597^716599)}g`);}}const _0xf8ea9f=[];for(let i=939319^939319;i<_0xae7bfc['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){if(!isNaN(_0xfb_0x46a[i])&&!isNaN(_0xae7bfc[i])){_0xf8ea9f['\u0070\u0075\u0073\u0068'](i);}}if(_0xf8ea9f['\u006C\u0065\u006E\u0067\u0074\u0068']===(975070^975070)){throw new Error("\u9694\u95F4 uat_atled \u6B63\u7684\u6548\u6709\u5230\u627E\u6709\u6CA1".split("").reverse().join(""));}const _0xfe71a=_0xf8ea9f['\u006D\u0061\u0070'](i=>_0xae7bfc[i]);var _0xb81efc=(220977^220978)+(145052^145054);const _0xb25ead=_0xf8ea9f['\u006D\u0061\u0070'](i=>_0xfb_0x46a[i]);_0xb81efc=935667^935674;return[_0xfe71a,_0xb25ead];}catch(error){throw new Error(`输入数据错误: ${error['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`);}}function powerModel(x,k,n,xe){return x['\u006D\u0061\u0070'](xi=>xi>xe?k*Math['\u0070\u006F\u0077'](xi-xe,n):692236^692236);}function polynomialModel(x,a,b,c){return x['\u006D\u0061\u0070'](xi=>a+b*xi+c*xi*xi);}function linearModel(x,m,c){return x['\u006D\u0061\u0070'](xi=>m*xi+c);}function calculateRSquared(yTrue,yPred){var _0x39974a=(567777^567776)+(588520^588523);const _0xb0716d=yTrue['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,y)=>sum+y,804489^804489)/yTrue['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x39974a=(829062^829070)+(297941^297936);var _0xb72bba=(410893^410893)+(903740^903737);const _0x8f47fc=yTrue['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,y)=>sum+Math['\u0070\u006F\u0077'](y-_0xb0716d,381699^381697),339830^339830);_0xb72bba=440501^440502;const _0xd8cfe=yTrue['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,y,i)=>sum+Math['\u0070\u006F\u0077'](y-yPred[i],674790^674788),325751^325751);return(394914^394915)-_0xd8cfe/_0x8f47fc;}function calculateRMSE(yTrue,yPred){const _0xdcg=yTrue['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,y,i)=>sum+Math['\u0070\u006F\u0077'](y-yPred[i],346667^346665),187207^187207)/yTrue['\u006C\u0065\u006E\u0067\u0074\u0068'];return Math['\u0073\u0071\u0072\u0074'](_0xdcg);}function linearRegression(x,y){if(!x||!y||x['\u006C\u0065\u006E\u0067\u0074\u0068']===(613455^613455)||y['\u006C\u0065\u006E\u0067\u0074\u0068']===(255866^255866)){throw new Error("\u8F93\u5165\u6570\u636E\u4E3A\u7A7A");}if(x['\u006C\u0065\u006E\u0067\u0074\u0068']!==y['\u006C\u0065\u006E\u0067\u0074\u0068']){throw new Error("\u0058\u548C\u0059\u6570\u636E\u957F\u5EA6\u4E0D\u5339\u914D");}if(x['\u006C\u0065\u006E\u0067\u0074\u0068']<(514953^514955)){throw new Error("\u5F52\u56DE\u6027\u7EBF\u884C\u8FDB\u70B9\u636E\u6570\u4E2A2\u8981\u9700\u5C11\u81F3".split("").reverse().join(""));}const n=x['\u006C\u0065\u006E\u0067\u0074\u0068'];for(let i=763942^763942;i<n;i++){if(!isFinite(x[i])||!isFinite(y[i])){throw new Error(`数据点 ${i} 包含无效值: x=${x[i]}, y=${y[i]}`);}}var _0x2a68f=(554710^554707)+(628965^628972);const _0x5f91f=x['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,xi)=>sum+xi,215212^215212);_0x2a68f=(363048^363051)+(967996^967994);var _0x31ecac=(370177^370177)+(965052^965051);const _0x57edb=y['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,yi)=>sum+yi,258064^258064);_0x31ecac=460833^460836;var _0x69797g;const _0xd0cdfc=x['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,xi,i)=>sum+xi*y[i],382547^382547);_0x69797g='\u0070\u006C\u0067\u0068\u006B\u0067';const _0x8a8e2a=x['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,xi)=>sum+xi*xi,190101^190101);const _0x7eb59a=n*_0x8a8e2a-_0x5f91f*_0x5f91f;if(Math['\u0061\u0062\u0073'](_0x7eb59a)<1e-12){throw new Error("\u7EBF\u6027\u56DE\u5F52\u5206\u6BCD\u63A5\u8FD1\u96F6\uFF0C\u6570\u636E\u53EF\u80FD\u5171\u7EBF");}var _0xc2da5e=(657319^657314)+(419391^419391);const _0xd30f3a=(n*_0xd0cdfc-_0x5f91f*_0x57edb)/_0x7eb59a;_0xc2da5e=344259^344257;const _0xe6af4f=(_0x57edb-_0xd30f3a*_0x5f91f)/n;if(!isFinite(_0xd30f3a)||!isFinite(_0xe6af4f)){throw new Error(`线性回归计算出无效参数: slope=${_0xd30f3a}, intercept=${_0xe6af4f}`);}const _0xe9egb=x['\u006D\u0061\u0070'](xi=>_0xd30f3a*xi+_0xe6af4f);const _0xdefa=calculateRSquared(y,_0xe9egb);if(!isFinite(_0xdefa)){throw new Error(`R²计算结果无效: ${_0xdefa}`);}return{"slope":_0xd30f3a,'\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074':_0xe6af4f,'\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064':_0xdefa,'\u0079\u0050\u0072\u0065\u0064':_0xe9egb};}function fitPowerModel(x,y){try{const _0x7a95cd=Math['\u006D\u0069\u006E'](...x);var _0x2gdae=(475297^475297)+(258230^258231);let _0x187c=Math['\u006D\u0061\u0078'](222922^222922,_0x7a95cd*0.5);_0x2gdae=(177069^177071)+(679613^679610);const _0x91c=[];for(let i=296251^296251;i<x['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){if(x[i]>_0x187c&&y[i]>(136501^136501)){_0x91c['\u0070\u0075\u0073\u0068'](i);}}if(_0x91c['\u006C\u0065\u006E\u0067\u0074\u0068']<(798054^798053)){throw new Error("\u70B9\u6548\u6709\u4E2A3\u5C11\u81F3\u8981\u9700\u5408\u62DF\u6570\u51FD\u5E42".split("").reverse().join(""));}const _0xfb8c=_0x91c['\u006D\u0061\u0070'](i=>x[i]-_0x187c);var _0x240a2b=(238441^238441)+(536499^536500);const _0x6e6c=_0x91c['\u006D\u0061\u0070'](i=>y[i]);_0x240a2b=739626^739630;var _0xa72e=(666863^666861)+(132520^132524);const _0xbb_0xfd1=_0xfb8c['\u006D\u0061\u0070'](xi=>Math['\u006C\u006F\u0067'](xi));_0xa72e=823397^823395;const _0xaff8f=_0x6e6c['\u006D\u0061\u0070'](yi=>Math['\u006C\u006F\u0067'](yi));var _0x2452e;const _0xbc9c9a=linearRegression(_0xbb_0xfd1,_0xaff8f);_0x2452e="igkeme".split("").reverse().join("");var _0x831efa=(197143^197142)+(975977^975977);const k=Math['\u0065\u0078\u0070'](_0xbc9c9a['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']);_0x831efa=302586^302589;const n=_0xbc9c9a['\u0073\u006C\u006F\u0070\u0065'];const _0xe5e9a=x['\u006D\u0061\u0070'](xi=>xi>_0x187c?k*Math['\u0070\u006F\u0077'](xi-_0x187c,n):257749^257749);var _0x95bf=(977267^977268)+(995067^995059);const _0xee72g=calculateRSquared(y,_0xe5e9a);_0x95bf='\u006B\u0065\u0064\u006E\u0068\u0063';return{'\u006B':k,'\u006E':n,'\u0078\u0065':_0x187c,'\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064':_0xee72g,'\u0079\u0050\u0072\u0065\u0064':_0xe5e9a};}catch(error){console['\u0077\u0061\u0072\u006E']("\u5E42\u51FD\u6570\u62DF\u5408\u5931\u8D25\uFF0C\u56DE\u9000\u5230\u7EBF\u6027\u6A21\u578B");const _0x66d=linearRegression(x,y);return{'\u006B':_0x66d['\u0073\u006C\u006F\u0070\u0065'],'\u006E':1,'\u0078\u0065':-_0x66d['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']/_0x66d['\u0073\u006C\u006F\u0070\u0065'],'\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064':_0x66d['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'],"yPred":_0x66d['\u0079\u0050\u0072\u0065\u0064']||x['\u006D\u0061\u0070'](xi=>_0x66d['\u0073\u006C\u006F\u0070\u0065']*xi+_0x66d['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']),'\u0066\u0061\u006C\u006C\u0062\u0061\u0063\u006B':!![]};}}function fitPolynomialModel(x,y){try{var _0xde94e=(260130^260134)+(944754^944762);const n=x['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xde94e=131945^131945;let _0x95e=216323^216323,_0x5_0x8ef=304676^304676,_0x7a1ab=629454^629454,_0xaaef0e=686309^686309;let _0x1f1ff=951148^951148,_0x7bbdg=267034^267034,_0xe7fb6e=733920^733920;for(let i=272714^272714;i<n;i++){var _0x4d_0x463=(566610^566611)+(288388^288389);const _0xcec3ab=x[i];_0x4d_0x463=581441^581441;const _0xac956a=y[i];const _0xe1f=_0xcec3ab*_0xcec3ab;var _0x45d=(266972^266968)+(541966^541967);const _0x93c=_0xe1f*_0xcec3ab;_0x45d=980061^980053;const _0x83b84e=_0x93c*_0xcec3ab;_0x95e+=_0xcec3ab;_0x5_0x8ef+=_0xe1f;_0x7a1ab+=_0x93c;_0xaaef0e+=_0x83b84e;_0x1f1ff+=_0xac956a;_0x7bbdg+=_0xcec3ab*_0xac956a;_0xe7fb6e+=_0xe1f*_0xac956a;}const _0xaf84c=n*(_0x5_0x8ef*_0xaaef0e-_0x7a1ab*_0x7a1ab)-_0x95e*(_0x95e*_0xaaef0e-_0x5_0x8ef*_0x7a1ab)+_0x5_0x8ef*(_0x95e*_0x7a1ab-_0x5_0x8ef*_0x5_0x8ef);if(Math['\u0061\u0062\u0073'](_0xaf84c)<1e-10){throw new Error("\u77E9\u9635\u5947\u5F02\uFF0C\u65E0\u6CD5\u6C42\u89E3");}const _0x2c3ccf=_0x1f1ff*(_0x5_0x8ef*_0xaaef0e-_0x7a1ab*_0x7a1ab)-_0x7bbdg*(_0x95e*_0xaaef0e-_0x5_0x8ef*_0x7a1ab)+_0xe7fb6e*(_0x95e*_0x7a1ab-_0x5_0x8ef*_0x5_0x8ef);var _0xa7g6a;const _0x3bgec=n*(_0x7bbdg*_0xaaef0e-_0xe7fb6e*_0x7a1ab)-_0x1f1ff*(_0x95e*_0xaaef0e-_0x5_0x8ef*_0x7a1ab)+_0x5_0x8ef*(_0x95e*_0xe7fb6e-_0x7bbdg*_0x5_0x8ef);_0xa7g6a=419436^419435;const _0x6_0xefd=n*(_0x5_0x8ef*_0xe7fb6e-_0x7a1ab*_0x7bbdg)-_0x95e*(_0x95e*_0xe7fb6e-_0x5_0x8ef*_0x7bbdg)+_0x1f1ff*(_0x95e*_0x7a1ab-_0x5_0x8ef*_0x5_0x8ef);const a=_0x2c3ccf/_0xaf84c;const b=_0x3bgec/_0xaf84c;var _0xc_0x07g=(975710^975706)+(499168^499168);const c=_0x6_0xefd/_0xaf84c;_0xc_0x07g='\u0069\u006A\u006C\u0069\u0063\u006C';var _0x42c31a;const _0x53e2e=x['\u006D\u0061\u0070'](xi=>a+b*xi+c*xi*xi);_0x42c31a=(922426^922418)+(975594^975593);const _0x81d=calculateRSquared(y,_0x53e2e);var _0x126d1a=(781081^781084)+(648229^648237);let _0x63163f=947662^947662;_0x126d1a=(310455^310455)+(919939^919942);if(Math['\u0061\u0062\u0073'](c)>1e-9){var _0x53de;const _0xff_0x1fe=b*b-(914312^914316)*a*c;_0x53de=(162023^162022)+(274394^274392);if(_0xff_0x1fe>=(417990^417990)){var _0x2d5b=(837605^837600)+(850731^850722);const _0xag_0x385=(-b+Math['\u0073\u0071\u0072\u0074'](_0xff_0x1fe))/((728650^728648)*c);_0x2d5b=534825^534825;const _0xf3248b=(-b-Math['\u0073\u0071\u0072\u0074'](_0xff_0x1fe))/((446100^446102)*c);var _0x2_0xa94;const _0xbf_0xa45=[_0xag_0x385,_0xf3248b]['\u0066\u0069\u006C\u0074\u0065\u0072'](root=>root>(434288^434288)&&root<Math['\u006D\u0069\u006E'](...x));_0x2_0xa94='\u006A\u0069\u0064\u0065\u006E\u006A';_0x63163f=_0xbf_0xa45['\u006C\u0065\u006E\u0067\u0074\u0068']>(317474^317474)?_0xbf_0xa45[517125^517125]:395519^395519;}}else if(Math['\u0061\u0062\u0073'](b)>1e-9){_0x63163f=-a/b;}return{'\u0061':a,'\u0062':b,'\u0063':c,'\u0078\u0065':_0x63163f,'\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064':_0x81d,"yPred":_0x53e2e};}catch(error){console['\u0077\u0061\u0072\u006E']("\u591A\u9879\u5F0F\u62DF\u5408\u5931\u8D25\uFF0C\u56DE\u9000\u5230\u7EBF\u6027\u6A21\u578B");var _0xeee=(806268^806267)+(589847^589844);const _0x9ec8e=linearRegression(x,y);_0xeee=(194756^194758)+(810230^810231);return{'\u0061':_0x9ec8e['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074'],'\u0062':_0x9ec8e['\u0073\u006C\u006F\u0070\u0065'],'\u0063':0,"xe":-_0x9ec8e['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']/_0x9ec8e['\u0073\u006C\u006F\u0070\u0065'],'\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064':_0x9ec8e['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'],'\u0079\u0050\u0072\u0065\u0064':_0x9ec8e['\u0079\u0050\u0072\u0065\u0064']||x['\u006D\u0061\u0070'](xi=>_0x9ec8e['\u0073\u006C\u006F\u0070\u0065']*xi+_0x9ec8e['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']),"fallback":!![]};}}function formatDisplayValue(value){if(value===null||value===undefined||isNaN(value))return"A/N".split("").reverse().join("");if(!isFinite(value))return value>(456648^456648)?"\u221E":"\u221E-".split("").reverse().join("");if(Math['\u0061\u0062\u0073'](value)>1e5||Math['\u0061\u0062\u0073'](value)<1e-3&&value!==(747194^747194)){return value['\u0074\u006F\u0045\u0078\u0070\u006F\u006E\u0065\u006E\u0074\u0069\u0061\u006C'](248215^248212);}else{return value['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](975736^975740);}}function analyzeDryingData(xData,uData,modelType="\u006C\u0069\u006E\u0065\u0061\u0072",_0x168g6f,_0x4g0f){_0x168g6f=548282^548281;_0x4g0f=756046^756045;if(!xData||!uData||xData['\u006C\u0065\u006E\u0067\u0074\u0068']!==uData['\u006C\u0065\u006E\u0067\u0074\u0068']){return{'\u0065\u0072\u0072\u006F\u0072':"\u65E0\u6548\u7684\u0020\u0058\u0020\u6216\u0020\u0055\u0020\u6570\u636E"};}if(xData['\u006C\u0065\u006E\u0067\u0074\u0068']<_0x168g6f+_0x4g0f){return{"error":`此拟合需要至少 ${_0x168g6f+_0x4g0f} 个有效的 X-U 数据点 (找到 ${xData['\u006C\u0065\u006E\u0067\u0074\u0068']} 个)`};}try{var _0x90218d=(545635^545636)+(755752^755753);const _0xf47f3e=xData['\u006D\u0061\u0070']((_,i)=>i)['\u0073\u006F\u0072\u0074']((a,b)=>xData[b]-xData[a]);_0x90218d=(820600^820605)+(516912^516915);var _0x7e1g0c;const _0x24df=_0xf47f3e['\u006D\u0061\u0070'](i=>xData[i]);_0x7e1g0c=643098^643096;const _0x1bd2d=_0xf47f3e['\u006D\u0061\u0070'](i=>uData[i]);var _0x194af;const _0x6a_0xfc8=_0x24df['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x194af=(188756^188765)+(492883^492881);console['\u006C\u006F\u0067'](`原始数据点数: ${_0x6a_0xfc8}`);console['\u006C\u006F\u0067'](`X数据范围: [${Math['\u006D\u0069\u006E'](..._0x24df)['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](696225^696229)}, ${Math['\u006D\u0061\u0078'](..._0x24df)['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](374310^374306)}]`);console['\u006C\u006F\u0067'](`排序后的X数据:`,_0x24df['\u006D\u0061\u0070'](x=>x['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](320879^320875)));let _0xga2f=null;var _0x4a68a=(877183^877175)+(282881^282880);let _0x77d6cb=null;_0x4a68a=(102892^102885)+(844591^844584);var _0x64fbe=(432266^432258)+(748066^748066);let _0x4504f=null;_0x64fbe=605811^605813;var _0x2f96gf=(728477^728477)+(255477^255476);let _0xdcd5e=-Infinity;_0x2f96gf="ggjjlj".split("").reverse().join("");for(let _0xf82d=_0x168g6f-(643353^643352);_0xf82d<_0x6a_0xfc8-_0x4g0f;_0xf82d++){var _0xeaad;const _0x12eff=Array['\u0066\u0072\u006F\u006D']({'\u006C\u0065\u006E\u0067\u0074\u0068':_0xf82d+(848178^848179)},(_,i)=>i);_0xeaad=(641588^641590)+(401979^401970);var _0x7dec=(817430^817431)+(173304^173309);const _0xa915b=_0x12eff['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,i)=>sum+_0x1bd2d[i],405824^405824)/_0x12eff['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x7dec=(726257^726260)+(352481^352488);const _0x1cce3e=_0x24df[_0xf82d];var _0xac6eef=(114711^114707)+(412192^412199);let _0x7_0xge0=[];_0xac6eef='\u006E\u006D\u006F\u0064\u0063\u006C';let _0x7c27b=[];var _0x662b=(597105^597108)+(990890^990889);let _0xc3ec=[];_0x662b=(959158^959153)+(152871^152865);for(let i=739763^739763;i<_0x6a_0xfc8;i++){if(_0x24df[i]<_0x1cce3e){_0x7_0xge0['\u0070\u0075\u0073\u0068'](i);_0x7c27b['\u0070\u0075\u0073\u0068'](_0x24df[i]);_0xc3ec['\u0070\u0075\u0073\u0068'](_0x1bd2d[i]);}}console['\u006C\u006F\u0067'](`临界点候选 @X=${_0x1cce3e['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](568603^568607)}: 策略1找到 ${_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']} 个小于临界点的点`);if(_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']<_0x4g0f){console['\u006C\u006F\u0067'](`  策略1点数不足，尝试策略2`);_0x7_0xge0=[];_0x7c27b=[];_0xc3ec=[];for(let i=_0xf82d+(766073^766072);i<_0x6a_0xfc8;i++){_0x7_0xge0['\u0070\u0075\u0073\u0068'](i);_0x7c27b['\u0070\u0075\u0073\u0068'](_0x24df[i]);_0xc3ec['\u0070\u0075\u0073\u0068'](_0x1bd2d[i]);}console['\u006C\u006F\u0067'](`  策略2选择了 ${_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']} 个点`);}if(_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']<_0x4g0f){console['\u006C\u006F\u0067'](`  跳过，降速段点数仍不足 (${_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']} < ${_0x4g0f})`);continue;}const _0xa2_0x8e8=_0x7c27b['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,x)=>sum+x*x,645296^645296)/_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']-Math['\u0070\u006F\u0077'](_0x7c27b['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,x)=>sum+x,521708^521708)/_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068'],560323^560321);const _0xd34c3b=_0xc3ec['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,u)=>sum+u*u,433696^433696)/_0xc3ec['\u006C\u0065\u006E\u0067\u0074\u0068']-Math['\u0070\u006F\u0077'](_0xc3ec['\u0072\u0065\u0064\u0075\u0063\u0065']((sum,u)=>sum+u,472627^472627)/_0xc3ec['\u006C\u0065\u006E\u0067\u0074\u0068'],707095^707093);if(_0xa2_0x8e8<1e-15||_0xd34c3b<1e-15){console['\u006C\u006F\u0067'](`  跳过，降速段数据方差不足`);continue;}try{const _0x0748b=linearRegression(_0x7c27b,_0xc3ec);console['\u006C\u006F\u0067'](`  验证结果: 降速段点数=${_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']}, R2=${_0x0748b['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](947266^947270)}`);console['\u006C\u006F\u0067'](`  降速段X范围: [${Math['\u006D\u0069\u006E'](..._0x7c27b)['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](613963^613967)}, ${Math['\u006D\u0061\u0078'](..._0x7c27b)['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](404125^404121)}]`);var _0x57681b=(288510^288511)+(361185^361190);const _0x288d=0.9;_0x57681b=(961613^961609)+(839698^839703);if(_0x0748b['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064']>=_0x288d){_0x77d6cb={'\u006C\u0061\u0073\u0074\u0043\u006F\u006E\u0073\u0074\u0049\u0064\u0078':_0xf82d,"xcTrial":_0x1cce3e,'\u0075\u0063\u0054\u0072\u0069\u0061\u006C':_0xa915b,"xFalling":_0x7c27b,"uFalling":_0xc3ec,'\u0066\u0061\u006C\u006C\u0069\u006E\u0067\u0049\u006E\u0064\u0069\u0063\u0065\u0073':_0x7_0xge0};console['\u006C\u006F\u0067'](`✓ 选择临界点 @X=${_0x1cce3e['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](206395^206399)}，包含 ${_0x7c27b['\u006C\u0065\u006E\u0067\u0074\u0068']} 个降速段点，R2=${_0x0748b['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](527219^527223)} >= ${_0x288d}`);break;}else{console['\u006C\u006F\u0067'](`  R2=${_0x0748b['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](947257^947261)} < ${_0x288d}，记录为备用选项`);if(_0x0748b['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064']>_0xdcd5e){_0xdcd5e=_0x0748b['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'];_0x4504f={'\u006C\u0061\u0073\u0074\u0043\u006F\u006E\u0073\u0074\u0049\u0064\u0078':_0xf82d,"xcTrial":_0x1cce3e,'\u0075\u0063\u0054\u0072\u0069\u0061\u006C':_0xa915b,'\u0078\u0046\u0061\u006C\u006C\u0069\u006E\u0067':_0x7c27b,"uFalling":_0xc3ec,'\u0066\u0061\u006C\u006C\u0069\u006E\u0067\u0049\u006E\u0064\u0069\u0063\u0065\u0073':_0x7_0xge0};}}}catch(error){console['\u006C\u006F\u0067'](`  验证失败: ${error['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`);continue;}}if(!_0x77d6cb){if(_0x4504f){_0x77d6cb=_0x4504f;console['\u006C\u006F\u0067'](`⚠ 未找到R²≥0.9的临界点，使用最佳备用选项 @X=${_0x77d6cb['\u0078\u0063\u0054\u0072\u0069\u0061\u006C']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](582200^582204)}，R2=${_0xdcd5e['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](814546^814550)}`);}else{return{'\u0065\u0072\u0072\u006F\u0072':`未能找到任何可行的临界点`};}}const{"lastConstIdx":lastConstIdx,"xcTrial":xcTrial,"ucTrial":ucTrial,"xFalling":xFalling,'\u0075\u0046\u0061\u006C\u006C\u0069\u006E\u0067':uFalling,'\u0066\u0061\u006C\u006C\u0069\u006E\u0067\u0049\u006E\u0064\u0069\u0063\u0065\u0073':fallingIndices}=_0x77d6cb;try{let _0x6f_0x148;let _0x0b1d;var _0xa_0x7ab;let _0x6e17ae;_0xa_0x7ab=(590771^590772)+(883902^883903);let _0x79a;if(modelType==="raenil".split("").reverse().join("")){_0x6f_0x148=linearRegression(xFalling,uFalling);_0x0b1d={'\u0073\u006C\u006F\u0070\u0065':_0x6f_0x148['\u0073\u006C\u006F\u0070\u0065'],'\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074':_0x6f_0x148['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']};_0x6e17ae="\u578B\u6A21\u6027\u7EBF".split("").reverse().join("");_0x79a=-_0x6f_0x148['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']/_0x6f_0x148['\u0073\u006C\u006F\u0070\u0065'];}else if(modelType==="\u0070\u006F\u0077\u0065\u0072"){_0x6f_0x148=fitPowerModel(xFalling,uFalling);if(_0x6f_0x148['\u0066\u0061\u006C\u006C\u0062\u0061\u0063\u006B']){_0x0b1d={'\u0073\u006C\u006F\u0070\u0065':_0x6f_0x148['\u006B'],'\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074':-_0x6f_0x148['\u006B']*_0x6f_0x148['\u0078\u0065']};_0x6e17ae="\u7EBF\u6027\u6A21\u578B\u0020\u0028\u5E42\u51FD\u6570\u62DF\u5408\u5931\u8D25\u0029";_0x79a=_0x6f_0x148['\u0078\u0065'];}else{_0x0b1d={'\u006B':_0x6f_0x148['\u006B'],'\u006E':_0x6f_0x148['\u006E'],'\u0078\u0065':_0x6f_0x148['\u0078\u0065']};_0x6e17ae="\u5E42\u51FD\u6570\u6A21\u578B";_0x79a=_0x6f_0x148['\u0078\u0065'];}}else if(modelType==="\u0070\u006F\u006C\u0079\u006E\u006F\u006D\u0069\u0061\u006C"){_0x6f_0x148=fitPolynomialModel(xFalling,uFalling);if(_0x6f_0x148['\u0066\u0061\u006C\u006C\u0062\u0061\u0063\u006B']){_0x0b1d={'\u0073\u006C\u006F\u0070\u0065':_0x6f_0x148['\u0062'],'\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074':_0x6f_0x148['\u0061']};_0x6e17ae="\u7EBF\u6027\u6A21\u578B\u0020\u0028\u591A\u9879\u5F0F\u62DF\u5408\u5931\u8D25\u0029";_0x79a=_0x6f_0x148['\u0078\u0065'];}else{_0x0b1d={'\u0061':_0x6f_0x148['\u0061'],'\u0062':_0x6f_0x148['\u0062'],'\u0063':_0x6f_0x148['\u0063']};_0x6e17ae="\u578B\u6A21\u5F0F\u9879\u591A".split("").reverse().join("");_0x79a=_0x6f_0x148['\u0078\u0065'];}}if(_0x6f_0x148['\u0073\u006C\u006F\u0070\u0065']!==undefined&&_0x6f_0x148['\u0073\u006C\u006F\u0070\u0065']<=1e-9){return{'\u0065\u0072\u0072\u006F\u0072':`拟合失败：斜率非正 (${_0x6f_0x148['\u0073\u006C\u006F\u0070\u0065']})`};}const _0xf42d=calculateRMSE(uFalling,_0x6f_0x148['\u0079\u0050\u0072\u0065\u0064']);_0xga2f={'\u0073\u0070\u006C\u0069\u0074\u0049\u0064\u0078':lastConstIdx,'\u0075\u0063':ucTrial,"xc":xcTrial,"xe":_0x79a,'\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064':_0x6f_0x148['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'],'\u0072\u006D\u0073\u0065':_0xf42d,"modelParams":_0x0b1d,"modelType":modelType,"modelName":_0x6e17ae,"numFittedPoints":xFalling['\u006C\u0065\u006E\u0067\u0074\u0068'],'\u0066\u0061\u006C\u006C\u0069\u006E\u0067\u0049\u006E\u0064\u0069\u0063\u0065\u0073':fallingIndices,"startFallingIdx":lastConstIdx+(721521^721520),"yPred":_0x6f_0x148['\u0079\u0050\u0072\u0065\u0064'],'\u0078\u0046\u0061\u006C\u006C\u0069\u006E\u0067':xFalling,"uFalling":uFalling};}catch(error){return{"error":`拟合失败: ${error['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`};}if(_0xga2f===null){return{'\u0065\u0072\u0072\u006F\u0072':`未能找到成功的 ${modelType} 拟合`};}var _0x2dfb=(206522^206526)+(866035^866034);const _0xb85eba=_0xga2f['\u0073\u0070\u006C\u0069\u0074\u0049\u0064\u0078']+(437862^437863);_0x2dfb=(191036^191038)+(292400^292400);var _0x274f=(204033^204038)+(613159^613167);const _0x9_0x2f3=`分析结果 (${_0xga2f['\u006D\u006F\u0064\u0065\u006C\u004E\u0061\u006D\u0065']}):\n`+`-----------------\n`+`恒速段: ${_0xb85eba} 点, UC = ${formatDisplayValue(_0xga2f['\u0075\u0063'])} kg/(m2·s)\n`+`降速段: ${_0xga2f['\u006E\u0075\u006D\u0046\u0069\u0074\u0074\u0065\u0064\u0050\u006F\u0069\u006E\u0074\u0073']} 点, 所有 X < ${formatDisplayValue(_0xga2f['\u0078\u0063'])} 的点\n`+`临界含水率 (Xc): ${formatDisplayValue(_0xga2f['\u0078\u0063'])} kg/kg\n`+`平衡含水率 (Xe): ${formatDisplayValue(_0xga2f['\u0078\u0065'])} kg/kg\n`+`拟合优度 R2: ${formatDisplayValue(_0xga2f['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'])}\n`+`均方根误差 RMSE: ${formatDisplayValue(_0xga2f['\u0072\u006D\u0073\u0065'])}`;_0x274f="kpnffl".split("").reverse().join("");return{..._0xga2f,'\u0072\u0065\u0073\u0075\u006C\u0074\u0073\u0054\u0065\u0078\u0074':_0x9_0x2f3,'\u0078\u0044\u0061\u0074\u0061\u0053\u006F\u0072\u0074\u0065\u0064':_0x24df,"uDataSorted":_0x1bd2d};}catch(error){return{'\u0065\u0072\u0072\u006F\u0072':`分析过程中发生错误: ${error['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`};}}function calculateXUData(){try{const _0x33c=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("cg-yrd".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065']);var _0x2badfc=(172249^172249)+(540139^540141);const _0xd7df6c=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("ecafrus-yrd".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065']);_0x2badfc='\u0067\u0067\u0063\u006C\u006C\u0070';var _0xbe9bf=(306199^306198)+(774343^774340);const _0x5dc=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0064\u0065\u006C\u0074\u0061\u002D\u0077")['\u0076\u0061\u006C\u0075\u0065']);_0xbe9bf=(421882^421882)+(732805^732800);var _0xea92d;const _0xdgdb=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0072\u0061\u0077\u002D\u0064\u0061\u0074\u0061")['\u0076\u0061\u006C\u0075\u0065'];_0xea92d='\u0070\u0064\u0070\u0065\u006B\u0063';const[xArray,uArray]=calculateXUFromRaw(_0xdgdb,_0x33c,_0xd7df6c,_0x5dc);calculatedXData=xArray;calculatedUData=uArray;var _0x9d_0xd32=(895478^895475)+(270842^270835);let _0x4379gd="\u8BA1\u7B97\u7684\u0020\u0058\u002D\u0055\u0020\u6570\u636E\u003A\u000A";_0x9d_0xd32=(173413^173415)+(965827^965826);_0x4379gd+="\u0058\u005F\u0041\u0056\u0009\u0009\u0055\u0020\u0028\u006B\u0067\u002F\u0028\u006D\u0032\u00B7\u0073\u0029\u0029\u000A";_0x4379gd+="\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u002D\u000A";for(let i=427241^427241;i<xArray['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){_0x4379gd+=`${formatDisplayValue(xArray[i])}\t\t${formatDisplayValue(uArray[i])}\n`;}document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("stluser-yrd".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x4379gd;plotDryingChart(xArray,uArray);return!![];}catch(error){alert(`计算错误: ${error['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`);document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0072\u0065\u0073\u0075\u006C\u0074\u0073")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=`错误: ${error['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`;return false;}}function analyzeSelectedModel(){if(!calculatedXData||!calculatedUData){alert("\u636E\u6570 U-X \u7B97\u8BA1\u5148\u8BF7".split("").reverse().join(""));return;}var _0xc3c;const _0x36b42f=document['\u0071\u0075\u0065\u0072\u0079\u0053\u0065\u006C\u0065\u0063\u0074\u006F\u0072']("\u0069\u006E\u0070\u0075\u0074\u005B\u006E\u0061\u006D\u0065\u003D\u0022\u0064\u0072\u0079\u002D\u006D\u006F\u0064\u0065\u006C\u0022\u005D\u003A\u0063\u0068\u0065\u0063\u006B\u0065\u0064")['\u0076\u0061\u006C\u0075\u0065'];_0xc3c=(201132^201129)+(255886^255882);var _0x76ba3f;const _0x79facf=analyzeDryingData(calculatedXData,calculatedUData,_0x36b42f);_0x76ba3f=348580^348581;if(_0x79facf['\u0065\u0072\u0072\u006F\u0072']){document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0072\u0065\u0073\u0075\u006C\u0074\u0073")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=`错误: ${_0x79facf['\u0065\u0072\u0072\u006F\u0072']}`;return;}currentModelResults[_0x36b42f]=_0x79facf;document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0072\u0065\u0073\u0075\u006C\u0074\u0073")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x79facf['\u0072\u0065\u0073\u0075\u006C\u0074\u0073\u0054\u0065\u0078\u0074'];plotDryingChartWithFit(calculatedXData,calculatedUData,_0x79facf);}function compareAllModels(_0x584b){if(!calculatedXData||!calculatedUData){alert("\u8BF7\u5148\u8BA1\u7B97\u0020\u0058\u002D\u0055\u0020\u6570\u636E");return;}var _0x631cbb=(930214^930211)+(544043^544045);const _0xc87d=["raenil".split("").reverse().join(""),"\u0070\u006F\u0077\u0065\u0072","laimonylop".split("").reverse().join("")];_0x631cbb=(140693^140693)+(199618^199618);const _0xa3_0x0c6={};var _0x7c_0xcf1;_0x584b="\u6A21\u578B\u6BD4\u8F83\u7ED3\u679C\u003A\u000A";_0x7c_0xcf1=(300953^300953)+(322476^322473);_0x584b+="\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u000A\u000A";for(const _0x549cd of _0xc87d){var _0x87g8c;const _0x1627dc=analyzeDryingData(calculatedXData,calculatedUData,_0x549cd);_0x87g8c=(902051^902052)+(168844^168837);if(!_0x1627dc['\u0065\u0072\u0072\u006F\u0072']){_0xa3_0x0c6[_0x549cd]=_0x1627dc;_0x584b+=`${_0x1627dc['\u006D\u006F\u0064\u0065\u006C\u004E\u0061\u006D\u0065']}:\n`;_0x584b+=`  R2 = ${formatDisplayValue(_0x1627dc['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'])}\n`;_0x584b+=`  RMSE = ${formatDisplayValue(_0x1627dc['\u0072\u006D\u0073\u0065'])}\n`;_0x584b+=`  Xc = ${formatDisplayValue(_0x1627dc['\u0078\u0063'])} kg/kg\n`;_0x584b+=`  Xe = ${formatDisplayValue(_0x1627dc['\u0078\u0065'])} kg/kg\n\n`;}else{_0x584b+=`${_0x549cd} 模型: 拟合失败 - ${_0x1627dc['\u0065\u0072\u0072\u006F\u0072']}\n\n`;}}var _0xb47aa;let _0x24b9db=null;_0xb47aa=(401403^401400)+(238557^238557);var _0x3264gc=(691369^691375)+(318156^318155);let _0x61g8b=-Infinity;_0x3264gc=(922461^922456)+(717812^717811);for(const[model,result]of Object['\u0065\u006E\u0074\u0072\u0069\u0065\u0073'](_0xa3_0x0c6)){if(result['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064']>_0x61g8b){_0x61g8b=result['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'];_0x24b9db=model;}}if(_0x24b9db){_0x584b+=`推荐模型: ${_0xa3_0x0c6[_0x24b9db]['\u006D\u006F\u0064\u0065\u006C\u004E\u0061\u006D\u0065']} (最高 R2 = ${formatDisplayValue(_0x61g8b)})`;}document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("stluser-yrd".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x584b;plotDryingComparisonChart(calculatedXData,calculatedUData,_0xa3_0x0c6);}function plotDryingChart(xData,uData){var _0x630bf;const _0xbc_0xf95=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u0069\u006E\u0067\u002D\u0070\u006C\u006F\u0074");_0x630bf="lhomml".split("").reverse().join("");if(!_0xbc_0xf95)return;const _0xbf37ca=[{'\u0078':xData,'\u0079':uData,"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":"\u006D\u0061\u0072\u006B\u0065\u0072\u0073","name":"\u5B9E\u9A8C\u6570\u636E",'\u006D\u0061\u0072\u006B\u0065\u0072':{'\u0063\u006F\u006C\u006F\u0072':"\u0062\u006C\u0075\u0065",'\u0073\u0069\u007A\u0065':8,"symbol":'circle'}}];var _0xe_0x49f=(994319^994311)+(263451^263455);const _0x89c6a={'\u0074\u0069\u0074\u006C\u0065':"\u5E72\u71E5\u901F\u7387\u66F2\u7EBF",'\u0078\u0061\u0078\u0069\u0073':{'\u0074\u0069\u0074\u006C\u0065':"\u5E73\u5747\u542B\u6C34\u7387\u0020\u0058\u0020\u0028\u006B\u0067\u002F\u006B\u0067\u0029",'\u0073\u0068\u006F\u0077\u0067\u0072\u0069\u0064':!![]},"yaxis":{"title":"\u5E72\u71E5\u901F\u7387\u0020\u0055\u0020\u0028\u006B\u0067\u002F\u0028\u006D\u0032\u00B7\u0073\u0029\u0029","showgrid":!![],'\u0074\u0069\u0063\u006B\u0066\u006F\u0072\u006D\u0061\u0074':"\u002E\u0032\u0065"},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':!![],'\u0066\u006F\u006E\u0074':{'\u0066\u0061\u006D\u0069\u006C\u0079':"\u004E\u006F\u0074\u006F\u0020\u0053\u0061\u006E\u0073\u0020\u0053\u0043\u002C\u0020\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066",'\u0073\u0069\u007A\u0065':12},'\u0077\u0069\u0064\u0074\u0068':600,'\u0068\u0065\u0069\u0067\u0068\u0074':500,'\u006D\u0061\u0072\u0067\u0069\u006E':{'\u006C':60,'\u0072':30,'\u0062':60,'\u0074':80,"pad":4}};_0xe_0x49f='\u0062\u0066\u0070\u006E\u0067\u0069';Plotly['\u006E\u0065\u0077\u0050\u006C\u006F\u0074'](_0xbc_0xf95,_0xbf37ca,_0x89c6a);}function plotDryingChartWithFit(xData,uData,fitResult,_0x267e){const _0x03a9db=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u0069\u006E\u0067\u002D\u0070\u006C\u006F\u0074");if(!_0x03a9db)return;var _0x3_0x21f;const _0x74ba5a=[{'\u0078':xData,'\u0079':uData,'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":"\u006D\u0061\u0072\u006B\u0065\u0072\u0073",'\u006E\u0061\u006D\u0065':"\u5B9E\u9A8C\u6570\u636E",'\u006D\u0061\u0072\u006B\u0065\u0072':{"color":'blue','\u0073\u0069\u007A\u0065':8,'\u0073\u0079\u006D\u0062\u006F\u006C':"\u0063\u0069\u0072\u0063\u006C\u0065"}}];_0x3_0x21f=(972095^972091)+(258376^258379);const _0xda11c=Math['\u006D\u0061\u0078'](...xData);const _0xb4691a=fitResult['\u0078\u0063']<_0xda11c?fitResult['\u0078\u0063']:_0xda11c;_0x74ba5a['\u0070\u0075\u0073\u0068']({'\u0078':[_0xb4691a,_0xda11c],'\u0079':[fitResult['\u0075\u0063'],fitResult['\u0075\u0063']],'\u0074\u0079\u0070\u0065':'scatter',"mode":"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':`恒速段 (UC=${formatDisplayValue(fitResult['\u0075\u0063'])})`,'\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':'red',"width":2,'\u0064\u0061\u0073\u0068':"\u0064\u0061\u0073\u0068"}});const _0x7575d=fitResult['\u0078\u0046\u0061\u006C\u006C\u0069\u006E\u0067']||fitResult['\u0066\u0061\u006C\u006C\u0069\u006E\u0067\u0049\u006E\u0064\u0069\u0063\u0065\u0073']['\u006D\u0061\u0070'](i=>fitResult['\u0078\u0044\u0061\u0074\u0061\u0053\u006F\u0072\u0074\u0065\u0064'][i]);var _0x7dbba;const _0xe57d7f=Math['\u006D\u0069\u006E'](..._0x7575d);_0x7dbba=(187338^187340)+(912811^912813);const _0x879de=Math['\u006D\u0061\u0078'](..._0x7575d);var _0x638e=(375461^375460)+(908107^908109);const _0xc9g=Array['\u0066\u0072\u006F\u006D']({'\u006C\u0065\u006E\u0067\u0074\u0068':50},(_,i)=>_0xe57d7f+(_0x879de-_0xe57d7f)*i/(854577^854528));_0x638e=(802663^802661)+(624023^624022);if(fitResult['\u006D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']==="raenil".split("").reverse().join("")||fitResult['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073']['\u0073\u006C\u006F\u0070\u0065']!==undefined){const _0x973b=fitResult['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073']['\u0073\u006C\u006F\u0070\u0065'];const _0x2658cb=fitResult['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073']['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074'];_0x267e=_0xc9g['\u006D\u0061\u0070'](x=>_0x973b*x+_0x2658cb);}else if(fitResult['\u006D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']==="\u0070\u006F\u0077\u0065\u0072"){const{'\u006B':k,'\u006E':n,'\u0078\u0065':xe}=fitResult['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073'];_0x267e=_0xc9g['\u006D\u0061\u0070'](x=>x>xe?k*Math['\u0070\u006F\u0077'](x-xe,n):915088^915088);}else if(fitResult['\u006D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']==="\u0070\u006F\u006C\u0079\u006E\u006F\u006D\u0069\u0061\u006C"){const{'\u0061':a,'\u0062':b,'\u0063':c}=fitResult['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073'];_0x267e=_0xc9g['\u006D\u0061\u0070'](x=>a+b*x+c*x*x);}if(_0x267e){_0x74ba5a['\u0070\u0075\u0073\u0068']({'\u0078':_0xc9g,'\u0079':_0x267e,'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":"\u006C\u0069\u006E\u0065\u0073","name":`${fitResult['\u006D\u006F\u0064\u0065\u006C\u004E\u0061\u006D\u0065']} (R2=${formatDisplayValue(fitResult['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'])})`,'\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':"\u0067\u0072\u0065\u0065\u006E",'\u0077\u0069\u0064\u0074\u0068':2}});}_0x74ba5a['\u0070\u0075\u0073\u0068']({'\u0078':[fitResult['\u0078\u0063']],'\u0079':[fitResult['\u0075\u0063']],"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073",'\u006E\u0061\u006D\u0065':`临界点 (Xc=${formatDisplayValue(fitResult['\u0078\u0063'])})`,"marker":{'\u0063\u006F\u006C\u006F\u0072':'black',"size":10,'\u0073\u0079\u006D\u0062\u006F\u006C':"\u0078"}});if(fitResult['\u0078\u0065']>(680364^680364)){_0x74ba5a['\u0070\u0075\u0073\u0068']({'\u0078':[fitResult['\u0078\u0065']],'\u0079':[970319^970319],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":'markers','\u006E\u0061\u006D\u0065':`平衡点 (Xe=${formatDisplayValue(fitResult['\u0078\u0065'])})`,"marker":{"color":'red','\u0073\u0069\u007A\u0065':10,"symbol":'diamond'}});}var _0xdcdb=(989704^989697)+(954505^954497);const _0xfc10ce={"title":`干燥速率曲线 - ${fitResult['\u006D\u006F\u0064\u0065\u006C\u004E\u0061\u006D\u0065']}拟合`,'\u0078\u0061\u0078\u0069\u0073':{"title":'平均含水率 X (kg/kg)',"showgrid":!![]},'\u0079\u0061\u0078\u0069\u0073':{'\u0074\u0069\u0074\u006C\u0065':'干燥速率 U (kg/(m2·s))','\u0073\u0068\u006F\u0077\u0067\u0072\u0069\u0064':!![],"tickformat":'.2e'},"showlegend":!![],"font":{'\u0066\u0061\u006D\u0069\u006C\u0079':"\u004E\u006F\u0074\u006F\u0020\u0053\u0061\u006E\u0073\u0020\u0053\u0043\u002C\u0020\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066",'\u0073\u0069\u007A\u0065':12},'\u0077\u0069\u0064\u0074\u0068':600,'\u0068\u0065\u0069\u0067\u0068\u0074':500,'\u006D\u0061\u0072\u0067\u0069\u006E':{'\u006C':60,'\u0072':30,'\u0062':60,'\u0074':80,"pad":4}};_0xdcdb='\u006A\u0062\u0066\u006C\u006D\u006B';Plotly['\u006E\u0065\u0077\u0050\u006C\u006F\u0074'](_0x03a9db,_0x74ba5a,_0xfc10ce);}function plotDryingComparisonChart(xData,uData,results){var _0xb5a18e=(976625^976627)+(678413^678414);const _0xf1ba2f=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("tolp-gniyrd".split("").reverse().join(""));_0xb5a18e=942561^942562;if(!_0xf1ba2f)return;const _0x2fe9e=[{'\u0078':xData,'\u0079':uData,'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073",'\u006E\u0061\u006D\u0065':"\u5B9E\u9A8C\u6570\u636E",'\u006D\u0061\u0072\u006B\u0065\u0072':{'\u0063\u006F\u006C\u006F\u0072':"\u0062\u006C\u0061\u0063\u006B",'\u0073\u0069\u007A\u0065':8,"symbol":"\u0063\u0069\u0072\u0063\u006C\u0065"}}];var _0x26gcaa=(845974^845972)+(236510^236509);const _0x774da={"linear":"\u0072\u0065\u0064","power":'green',"polynomial":'blue'};_0x26gcaa=(407879^407876)+(140815^140806);const _0x71dd2e={'\u006C\u0069\u006E\u0065\u0061\u0072':"\u0073\u006F\u006C\u0069\u0064","power":"\u0064\u0061\u0073\u0068",'\u0070\u006F\u006C\u0079\u006E\u006F\u006D\u0069\u0061\u006C':'dot'};for(const[modelType,result]of Object['\u0065\u006E\u0074\u0072\u0069\u0065\u0073'](results)){if(result['\u0065\u0072\u0072\u006F\u0072'])continue;const _0xe1g=result['\u0078\u0046\u0061\u006C\u006C\u0069\u006E\u0067']||result['\u0066\u0061\u006C\u006C\u0069\u006E\u0067\u0049\u006E\u0064\u0069\u0063\u0065\u0073']['\u006D\u0061\u0070'](i=>result['\u0078\u0044\u0061\u0074\u0061\u0053\u006F\u0072\u0074\u0065\u0064'][i]);const _0x5ed=Math['\u006D\u0069\u006E'](..._0xe1g);const _0xafda=Math['\u006D\u0061\u0078'](..._0xe1g);const _0x08c=Array['\u0066\u0072\u006F\u006D']({"length":50},(_,i)=>_0x5ed+(_0xafda-_0x5ed)*i/(103281^103232));let _0x4c_0x261;if(modelType==="\u006C\u0069\u006E\u0065\u0061\u0072"||result['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073']['\u0073\u006C\u006F\u0070\u0065']!==undefined){const _0x6705a=result['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073']['\u0073\u006C\u006F\u0070\u0065'];const _0xacf=result['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073']['\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074'];_0x4c_0x261=_0x08c['\u006D\u0061\u0070'](x=>_0x6705a*x+_0xacf);}else if(modelType==="rewop".split("").reverse().join("")){const{'\u006B':k,'\u006E':n,"xe":xe}=result['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073'];_0x4c_0x261=_0x08c['\u006D\u0061\u0070'](x=>x>xe?k*Math['\u0070\u006F\u0077'](x-xe,n):612835^612835);}else if(modelType==="\u0070\u006F\u006C\u0079\u006E\u006F\u006D\u0069\u0061\u006C"){const{'\u0061':a,'\u0062':b,'\u0063':c}=result['\u006D\u006F\u0064\u0065\u006C\u0050\u0061\u0072\u0061\u006D\u0073'];_0x4c_0x261=_0x08c['\u006D\u0061\u0070'](x=>a+b*x+c*x*x);}if(_0x4c_0x261){_0x2fe9e['\u0070\u0075\u0073\u0068']({'\u0078':_0x08c,'\u0079':_0x4c_0x261,"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':`${result['\u006D\u006F\u0064\u0065\u006C\u004E\u0061\u006D\u0065']} (R2=${formatDisplayValue(result['\u0072\u0053\u0071\u0075\u0061\u0072\u0065\u0064'])})`,'\u006C\u0069\u006E\u0065':{"color":_0x774da[modelType],"width":2,'\u0064\u0061\u0073\u0068':_0x71dd2e[modelType]}});}}const _0x7f_0xf00={'\u0074\u0069\u0074\u006C\u0065':"\u5E72\u71E5\u901F\u7387\u66F2\u7EBF\u0020\u002D\u0020\u6A21\u578B\u6BD4\u8F83","xaxis":{"title":"\u5E73\u5747\u542B\u6C34\u7387\u0020\u0058\u0020\u0028\u006B\u0067\u002F\u006B\u0067\u0029",'\u0073\u0068\u006F\u0077\u0067\u0072\u0069\u0064':!![]},"yaxis":{'\u0074\u0069\u0074\u006C\u0065':'干燥速率 U (kg/(m2·s))','\u0073\u0068\u006F\u0077\u0067\u0072\u0069\u0064':!![],'\u0074\u0069\u0063\u006B\u0066\u006F\u0072\u006D\u0061\u0074':"\u002E\u0032\u0065"},"showlegend":!![],'\u0066\u006F\u006E\u0074':{'\u0066\u0061\u006D\u0069\u006C\u0079':"\u004E\u006F\u0074\u006F\u0020\u0053\u0061\u006E\u0073\u0020\u0053\u0043\u002C\u0020\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066",'\u0073\u0069\u007A\u0065':12},"width":600,'\u0068\u0065\u0069\u0067\u0068\u0074':500,"margin":{'\u006C':60,'\u0072':30,'\u0062':60,'\u0074':80,"pad":4}};Plotly['\u006E\u0065\u0077\u0050\u006C\u006F\u0074'](_0xf1ba2f,_0x2fe9e,_0x7f_0xf00);}function loadDataset(){const _0xd_0x2ec=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0064\u0061\u0074\u0061\u0073\u0065\u0074")['\u0076\u0061\u006C\u0075\u0065'];var _0x519d=(968328^968328)+(511655^511655);const _0x2d019e=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("atad-war-yrd".split("").reverse().join(""));_0x519d=(920780^920783)+(755054^755046);var _0xba700b;const _0x13_0xcb2=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("cg-yrd".split("").reverse().join(""));_0xba700b=771795^771802;var _0x160g;const _0x0aa06g=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("ecafrus-yrd".split("").reverse().join(""));_0x160g=697797^697795;var _0xbcgdb=(616385^616393)+(714453^714452);const _0x13f98a=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("w-atled-yrd".split("").reverse().join(""));_0xbcgdb=302020^302019;if(_0xd_0x2ec==="\u0063\u0075\u0073\u0074\u006F\u006D"){return;}if(PREDEFINED_DATASETS[_0xd_0x2ec]){_0x2d019e['\u0076\u0061\u006C\u0075\u0065']=PREDEFINED_DATASETS[_0xd_0x2ec];if(_0xd_0x2ec==="\u0064\u0065\u0066\u0061\u0075\u006C\u0074"){_0x13_0xcb2['\u0076\u0061\u006C\u0075\u0065']="02".split("").reverse().join("");_0x0aa06g['\u0076\u0061\u006C\u0075\u0065']="\u0033\u0030\u0038";_0x13f98a['\u0076\u0061\u006C\u0075\u0065']="0.2".split("").reverse().join("");}else if(_0xd_0x2ec==="1tesatad".split("").reverse().join("")||_0xd_0x2ec==="2tesatad".split("").reverse().join("")){_0x13_0xcb2['\u0076\u0061\u006C\u0075\u0065']="\u0032\u0035";_0x0aa06g['\u0076\u0061\u006C\u0075\u0065']="003".split("").reverse().join("");_0x13f98a['\u0076\u0061\u006C\u0075\u0065']="0.2".split("").reverse().join("");}}}function clearRawData(){document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0072\u0061\u0077\u002D\u0064\u0061\u0074\u0061")['\u0076\u0061\u006C\u0075\u0065']='';document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0072\u0065\u0073\u0075\u006C\u0074\u0073")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u8BF7\u6309\u7167\u4EE5\u4E0B\u6B65\u9AA4\u64CD\u4F5C\uFF1A\u000A\u0031\u002E\u0020\u9009\u62E9\u6570\u636E\u96C6\u6216\u8F93\u5165\u539F\u59CB\u6570\u636E\u000A\u0032\u002E\u0020\u8BBE\u7F6E\u5B9E\u9A8C\u5E38\u6570\u0020\u0028\u0047\u0063\u002C\u0020\u0053\u002C\u0020\u0394\u0057\u0029\u000A\u0033\u002E\u0020\u70B9\u51FB\u0022\u8BA1\u7B97\u0058\u002D\u0055\u6570\u636E\u0022\u000A\u0034\u002E\u0020\u9009\u62E9\u62DF\u5408\u6A21\u578B\u5E76\u70B9\u51FB\u0022\u5206\u6790\u9009\u5B9A\u6A21\u578B\u0022\u000A\u0035\u002E\u0020\u6216\u70B9\u51FB\u0022\u6BD4\u8F83\u6240\u6709\u6A21\u578B\u0022\u67E5\u770B\u6240\u6709\u62DF\u5408\u7ED3\u679C";const _0xd964eb=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("tolp-gniyrd".split("").reverse().join(""));if(_0xd964eb){Plotly['\u0070\u0075\u0072\u0067\u0065'](_0xd964eb);}calculatedXData=null;calculatedUData=null;currentModelResults={};}document['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("dedaoLtnetnoCMOD".split("").reverse().join(""),function(){var _0xf5f=(975730^975731)+(691723^691723);const _0x84172e=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u006C\u006F\u0061\u0064\u002D\u0064\u0061\u0074\u0061\u0073\u0065\u0074");_0xf5f=126099^126098;if(_0x84172e){_0x84172e['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u006C\u0069\u0063\u006B",loadDataset);}const _0xe1ffff=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("tesatad-yrd".split("").reverse().join(""));if(_0xe1ffff){_0xe1ffff['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u0068\u0061\u006E\u0067\u0065",loadDataset);}var _0x8c97e=(643519^643513)+(478450^478451);const _0x9259b=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0063\u0061\u006C\u0063\u0075\u006C\u0061\u0074\u0065\u002D\u0078\u0075");_0x8c97e=150460^150460;if(_0x9259b){_0x9259b['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u006C\u0069\u0063\u006B",calculateXUData);}const _0xf3_0x77f=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("atad-raelc-yrd".split("").reverse().join(""));if(_0xf3_0x77f){_0xf3_0x77f['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u006C\u0069\u0063\u006B",clearRawData);}var _0xdbb;const _0x5e1e=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0061\u006E\u0061\u006C\u0079\u007A\u0065");_0xdbb='\u006B\u006D\u006C\u0065\u0065\u006E';if(_0x5e1e){_0x5e1e['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u006C\u0069\u0063\u006B",analyzeSelectedModel);}const _0x8af4ff=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0064\u0072\u0079\u002D\u0063\u006F\u006D\u0070\u0061\u0072\u0065");if(_0x8af4ff){_0x8af4ff['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("kcilc".split("").reverse().join(""),compareAllModels);}});