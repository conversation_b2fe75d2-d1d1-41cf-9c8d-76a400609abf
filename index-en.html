<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home</title>
    <!-- Page version: v1.1.1 - Updated: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Global scrollbar styles */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        /* Header navigation styles */
        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .hero-section {
            min-height: calc(100vh - 80px);
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(0, 114, 178, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(213, 94, 0, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23000" opacity="0.01"/><circle cx="10" cy="90" r="0.5" fill="%23000" opacity="0.01"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.4), transparent);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .card:hover::before {
            opacity: 1;
            height: 3px;
        }

        /* Button styles */
        .btn {
            background: linear-gradient(135deg, var(--primary-blue), #0056b3);
            color: white;
            padding: 0.875rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            display: inline-block;
            text-decoration: none;
            text-align: center;
            border: none;
            outline: none;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
            color: white;
            text-decoration: none;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:active {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        /* Announcement board styles */
        .announcement-board {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .announcement-board::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.4), transparent);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .announcement-board:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .announcement-board:hover::before {
            opacity: 1;
            height: 3px;
        }

        .announcement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(0, 114, 178, 0.1);
        }

        .announcement-header-left {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-blue);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .announcement-header-left i {
            font-size: 1.2rem;
            color: var(--primary-orange);
        }

        .announcement-content {
            flex: 1;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .announcement-slider {
            position: relative;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .announcement-slide {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .announcement-slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .news-date {
            color: var(--text-muted);
            font-size: 0.875rem;
            font-weight: 500;
            text-align: left;
        }

        .news-title {
            color: var(--primary-blue);
            font-weight: 700;
            font-size: 1rem;
            line-height: 1.4;
            margin-bottom: 0.5rem;
            text-align: justify;
            text-justify: inter-ideograph;
            word-break: break-word;
        }

        .news-description {
            color: var(--text-muted);
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: justify;
            text-justify: inter-ideograph;
            word-break: break-word;
            flex: 1;
        }

        .announcement-indicators {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: auto;
            padding-top: 1rem;
        }

        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(0, 114, 178, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: var(--primary-blue);
            transform: scale(1.2);
        }

        .indicator:hover {
            background: var(--primary-orange);
            transform: scale(1.1);
        }

        .more-btn {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: var(--text-muted);
            font-size: 0.875rem;
            font-weight: 500;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }

        .more-btn:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.05);
        }

        .more-btn i {
            font-size: 0.75rem;
            transition: transform 0.3s ease;
        }

        .more-btn:hover i {
            transform: translateX(2px);
        }

        .celebration-emoji {
            font-size: 1.1em;
            margin: 0 0.2rem;
            display: inline-block;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* 高级感标题样式 */
        .elegant-title {
            position: relative;
            display: inline-block;
            font-family: 'Georgia', 'Times New Roman', serif;
            font-weight: 700;
            background: linear-gradient(135deg, #006699 0%, #0072B2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 0.05em;
            line-height: 1.4;
            transition: all 0.4s ease;
        }

        /* 简化的光泽效果 */
        @keyframes textShimmer {
            0% {
                background-position: -100% center;
            }
            100% {
                background-position: 100% center;
            }
        }

        .elegant-title.shimmer {
            background: linear-gradient(
                90deg,
                #006699 0%,
                #0072B2 40%,
                #4A9FD9 50%,
                #0072B2 60%,
                #006699 100%
            );
            background-size: 150% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShimmer 6s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index-en.html" class="nav-link active">Home</a>
                <a href="profile-en.html" class="nav-link">Profile</a>
                <a href="research-en.html" class="nav-link">Research</a>
                <a href="members-en.html" class="nav-link">Members</a>
                <a href="index.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>中文
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-20">
            <h1 class="text-4xl md:text-5xl font-bold mb-8 leading-tight tracking-tight elegant-title shimmer">
                Catalyzing Tomorrow
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
                Focusing on bio and biomimetic catalysis research, committed to promoting efficient conversion of biomass resources
            </p>
            <div class="w-32 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-8 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-6 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <!-- Main Content Cards -->
        <div class="grid md:grid-cols-3 gap-8 mt-16">
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-user-tie text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">Team Leader</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">Academic background, research interests and educational experience</p>
                <a href="profile-en.html" class="btn mt-auto">View Details &rarr;</a>
            </div>

            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-green-50 via-green-100 to-green-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-users text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">Team Members</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">Information about research team members</p>
                <a href="members-en.html" class="btn mt-auto">View Members &rarr;</a>
            </div>

            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-flask text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">Research Directions</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">High-value utilization of lignin and biomimetic catalysis</p>
                <a href="research-en.html" class="btn mt-auto">Explore Research &rarr;</a>
            </div>
        </div>


    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme Insights. All Rights Reserved.</p>
            <p class="text-gray-300 text-lg">This website content is for research and teaching sharing only, without any commercial purpose. ICP No. 2025423918.</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // Basic page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Page load completion initialization
            console.log('English homepage loaded');

            // Add version number to page to prevent cache issues
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
