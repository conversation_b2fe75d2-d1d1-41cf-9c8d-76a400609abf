// 吸收计算工具 JavaScript 实现
// 基于 Abs.py 的逻辑

// --- 亨利常数数据 (温度 °C : E kPa) ---
const henryData = {
    0: 73800, 5: 88800, 10: 105000, 15: 124000, 20: 144000,
    25: 166000, 30: 188000, 35: 212000, 40: 236000,
    45: 260000, 50: 287000, 60: 346000
};

// --- 常数定义 ---
const R_GAS = 8.314; // kPa·m³/(kmol·K)
const WATER_MOLAR_MASS = 18.015; // kg/kmol
const WATER_DENSITY = 1000; // kg/m³
const PACKING_HEIGHT_Z = 1.3; // m
const FIXED_AREA_M2 = 0.00785; // m²
const TOWER_DIAMETER_M = 0.1; // m
const FIXED_X2 = 0.0; // 入口液相摩尔比

// V0 计算的标准条件
const T1_CELSIUS = 20.0; // 标定温度 (°C)
const P1_KPA = 101.325; // 标定压力 (kPa)
const T0_CELSIUS = 0.0; // 标准温度 (°C)
const P0_KPA = 101.325; // 标准压力 (kPa)
const STANDARD_MOLAR_VOLUME = 22.4; // m³/kmol

// --- 辅助函数 ---
function interpolateHenryE(tempC) {
    // 插值计算亨利常数因子 E
    const temps = Object.keys(henryData).map(Number).sort((a, b) => a - b);
    const eValues = temps.map(t => henryData[t]);

    if (tempC <= temps[0]) return eValues[0];
    if (tempC >= temps[temps.length - 1]) return eValues[eValues.length - 1];

    for (let i = 0; i < temps.length - 1; i++) {
        if (temps[i] <= tempC && tempC <= temps[i + 1]) {
            const t = (tempC - temps[i]) / (temps[i + 1] - temps[i]);
            return eValues[i] * (1 - t) + eValues[i + 1] * t;
        }
    }
    return null;
}

function calculateHenryConstants(tempC, totalPressureKpa) {
    // 计算亨利常数因子 E 和平衡常数 m
    const E_kPa = interpolateHenryE(tempC);
    if (E_kPa === null) return [null, null];

    if (Math.abs(totalPressureKpa) < 1e-9) {
        return [E_kPa, null];
    }

    const m = E_kPa / totalPressureKpa;
    return [E_kPa, m];
}

function moleFractionToRatio(yFraction) {
    // 摩尔分数转换为摩尔比
    if (yFraction >= 1.0 - 1e-9) {
        throw new Error("摩尔分数不能为 100% (y≈1)");
    }
    if (yFraction < 0) {
        throw new Error("摩尔分数不能为负数");
    }

    const denominator = 1.0 - yFraction;
    if (Math.abs(denominator) < 1e-12) {
        return Infinity;
    }
    return yFraction / denominator;
}

function calculateV0Standard(v1MeasuredM3h, t2Celsius, p2Kpa) {
    // 计算标准体积流量 V0
    const T0_K = T0_CELSIUS + 273.15;
    const T1_K = T1_CELSIUS + 273.15;
    const T2_K = t2Celsius + 273.15;

    if (v1MeasuredM3h < 0) throw new Error("实测流量 V1 不能为负");
    if (T0_K <= 0 || T1_K <= 0 || T2_K <= 0) throw new Error("绝对温度必须 > 0 K");
    if (P0_KPA <= 0 || P1_KPA <= 0 || p2Kpa <= 0) throw new Error("绝对压力必须 > 0 kPa");

    if (Math.abs(P0_KPA) < 1e-12) throw new Error("标准压力 P0 不能为零");

    const term2Den = T1_K * T2_K;
    if (Math.abs(term2Den) < 1e-12) throw new Error("计算 V0 时 T1*T2 为零");

    const part1 = T0_K / P0_KPA;
    const term2Num = P1_KPA * p2Kpa;
    const term2InsideSqrt = term2Num / term2Den;

    if (term2InsideSqrt < 0) {
        throw new Error(`计算 V0 时 sqrt 内为负数 (${term2InsideSqrt.toFixed(4)})`);
    }

    const part2Sqrt = Math.sqrt(term2InsideSqrt);
    const v0StandardM3h = v1MeasuredM3h * part1 * part2Sqrt;

    return v0StandardM3h;
}

function calculateNOLColburn(A, Y1, X1, X2, m) {
    // 基于 Colburn 公式计算传质单元数 (A != 1)
    if (Math.abs(A - 1) < 1e-9) {
        throw new Error("A 不能等于 1 (除零错误)");
    }

    const denominator = Y1 - m * X1;
    if (Math.abs(denominator) < 1e-9) {
        throw new Error("Y1 - m * X1 不能为零 (夹紧条件)");
    }

    const logArgument = (1 - A) * (Y1 - m * X2) / denominator + A;
    if (logArgument <= 0) {
        throw new Error("自然对数的参数必须为正数");
    }

    const NOL = (1 / (1 - A)) * Math.log(logArgument);
    return NOL;
}

// --- 格式化显示值 ---
function formatDisplayValue(value) {
    if (value === null || value === undefined || isNaN(value)) return "N/A";
    if (!isFinite(value)) return value > 0 ? "∞" : "-∞";

    if (Math.abs(value) > 1e5 || (Math.abs(value) < 1e-3 && value !== 0)) {
        return value.toExponential(3);
    } else {
        return value.toFixed(4);
    }
}

// --- 格式化摩尔比显示值（强制使用科学计数法）---
function formatMolarRatioValue(value) {
    if (value === null || value === undefined || isNaN(value)) return "N/A";
    if (!isFinite(value)) return value > 0 ? "∞" : "-∞";

    // 对于摩尔比，总是使用科学计数法
    return value.toExponential(3);
}

// --- 主计算函数 ---
function performAbsorptionCalculations() {
    try {
        // 清空结果显示
        clearAbsorptionResults();

        // 获取输入参数
        const v1InputM3h = parseFloat(document.getElementById('abs-air-flow').value);
        const airTempC = parseFloat(document.getElementById('abs-air-temp').value);
        const waterVolFlowLh = parseFloat(document.getElementById('abs-water-flow').value);
        const y1Percent = parseFloat(document.getElementById('abs-co2-inlet').value);
        const y2Percent = parseFloat(document.getElementById('abs-co2-outlet').value);
        const waterTempC = parseFloat(document.getElementById('abs-water-temp').value);
        const totalPressureKpa = parseFloat(document.getElementById('abs-pressure').value);

        // 输入验证
        if (v1InputM3h <= 0 || waterVolFlowLh <= 0) {
            throw new Error("气/水体积流量必须为正数");
        }
        if (!(0 <= y1Percent && y1Percent < 100 && 0 <= y2Percent && y2Percent < 100)) {
            throw new Error("CO2 浓度(%) 必须介于 [0, 100) 之间");
        }
        if (y1Percent <= y2Percent) {
            throw new Error("入口 CO2 浓度 (y1) 必须大于出口浓度 (y2)");
        }
        if (totalPressureKpa <= 0) {
            throw new Error("系统总压必须为正数");
        }

        // 计算 V0 和气相摩尔通量
        const V0StandardM3h = calculateV0Standard(v1InputM3h, airTempC, totalPressureKpa);
        const VKmolM2h = V0StandardM3h / STANDARD_MOLAR_VOLUME / FIXED_AREA_M2;

        // 计算液相摩尔通量
        const waterVolFlowM3h = waterVolFlowLh / 1000.0;
        const waterMassFlowKgh = waterVolFlowM3h * WATER_DENSITY;
        const waterMolarFlowKmolh = waterMassFlowKgh / WATER_MOLAR_MASS;
        const LKmolM2h = waterMolarFlowKmolh / FIXED_AREA_M2;

        // 计算亨利常数
        const [EKpa, m] = calculateHenryConstants(waterTempC, totalPressureKpa);
        if (EKpa === null) throw new Error(`无法计算 ${waterTempC}°C 下的亨利常数因子 E`);
        if (m === null) throw new Error("无法计算平衡常数 m (总压可能过低或 E 计算失败)");

        // 计算摩尔比
        const y1Frac = y1Percent / 100.0;
        const y2Frac = y2Percent / 100.0;
        const Y1 = moleFractionToRatio(y1Frac);
        const Y2 = moleFractionToRatio(y2Frac);

        // 计算液气比和出口液相摩尔比
        const calculatedLVRatio = LKmolM2h / VKmolM2h;
        const X1 = FIXED_X2 + (1.0 / calculatedLVRatio) * (Y1 - Y2);

        // 计算吸收因子
        let A = NaN;
        if (m !== null && !isNaN(m) && isFinite(m) && Math.abs(m) > 1e-12) {
            A = calculatedLVRatio / m;
        }

        // 计算传质单元数 NOL
        let NOLResult = NaN;
        if (!isNaN(A) && isFinite(A) && !isNaN(Y1) && !isNaN(Y2) && !isNaN(X1) && m > 0) {
            if (Math.abs(A - 1.0) < 1e-9) {
                // A ≈ 1 的情况
                const drivingForceBottom = Y1 - m * X1;
                const drivingForceTop = Y2 - m * FIXED_X2;
                if (drivingForceBottom > 1e-12 && drivingForceTop > 1e-12) {
                    const deltaYAvg = (drivingForceBottom + drivingForceTop) / 2.0;
                    if (Math.abs(deltaYAvg) > 1e-12) {
                        NOLResult = (Y1 - Y2) / deltaYAvg;
                    }
                }
            } else {
                try {
                    NOLResult = calculateNOLColburn(A, Y1, X1, FIXED_X2, m);
                } catch (e) {
                    console.warn("NOL 计算错误:", e.message);
                    NOLResult = NaN;
                }
            }
        }

        // 计算传质单元高度和传质系数
        let HOLResult = NaN;
        let KxaResult = NaN;

        if (!isNaN(NOLResult) && isFinite(NOLResult) && NOLResult > 1e-12) {
            HOLResult = PACKING_HEIGHT_Z / NOLResult;
        } else if (!isNaN(NOLResult) && Math.abs(NOLResult) <= 1e-12) {
            HOLResult = Infinity;
        } else if (isFinite(NOLResult) && NOLResult < 0) {
            HOLResult = 0.0;
        }

        if (!isNaN(HOLResult) && isFinite(HOLResult) && Math.abs(HOLResult) > 1e-12) {
            KxaResult = LKmolM2h / HOLResult;
        } else if (HOLResult === Infinity) {
            KxaResult = 0.0;
        } else if (!isNaN(HOLResult) && Math.abs(HOLResult) <= 1e-12) {
            KxaResult = Infinity;
        }

        // 计算吸收率
        let absorpRatePercent = NaN;
        if (!isNaN(Y1) && !isNaN(Y2) && Math.abs(Y1) > 1e-12) {
            absorpRatePercent = 100.0 * (Y1 - Y2) / Y1;
        }

        // 更新结果显示
        updateAbsorptionResults({
            VFlux: VKmolM2h,
            LFlux: LKmolM2h,
            HenryE: EKpa,
            EquilibriumM: m,
            LVRatio: calculatedLVRatio,
            AbsorptionFactor: A,
            NOL: NOLResult,
            HOL: HOLResult,
            Kxa: KxaResult,
            AbsorptionRate: absorpRatePercent,
            Y1: Y1,
            Y2: Y2,
            X1: X1,
            X2: FIXED_X2
        });

        // 绘制操作线与平衡线图
        plotAbsorptionChart({
            Y1, Y2, X1, X2: FIXED_X2, m, calculatedLVRatio
        });

    } catch (error) {
        alert(`计算错误: ${error.message}`);
        console.error("吸收计算错误:", error);
    }
}

function clearAbsorptionResults() {
    const resultIds = [
        'abs-v-flux', 'abs-l-flux', 'abs-henry-e', 'abs-equilibrium-m',
        'abs-lv-ratio', 'abs-absorption-factor', 'abs-nol', 'abs-hol',
        'abs-absorption-rate'
    ];

    resultIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.textContent = '--';
    });
}

function updateAbsorptionResults(results) {
    document.getElementById('abs-v-flux').textContent = formatDisplayValue(results.VFlux);
    document.getElementById('abs-l-flux').textContent = formatDisplayValue(results.LFlux);
    document.getElementById('abs-henry-e').textContent = formatDisplayValue(results.HenryE);
    document.getElementById('abs-equilibrium-m').textContent = formatDisplayValue(results.EquilibriumM);
    document.getElementById('abs-lv-ratio').textContent = formatDisplayValue(results.LVRatio);
    document.getElementById('abs-absorption-factor').textContent = formatDisplayValue(results.AbsorptionFactor);
    document.getElementById('abs-nol').textContent = formatDisplayValue(results.NOL);
    document.getElementById('abs-hol').textContent = formatDisplayValue(results.HOL);
    document.getElementById('abs-absorption-rate').textContent = formatDisplayValue(results.AbsorptionRate);
}

// --- 绘图函数 ---
function plotAbsorptionChart(data) {
    const plotDiv = document.getElementById('absorption-plot');
    if (!plotDiv) return;

    const { Y1, Y2, X1, X2, m, calculatedLVRatio } = data;

    // 创建操作线数据
    const opLineX = [X2, X1];
    const opLineY = [Y2, Y1];

    // 计算动态坐标轴范围
    const xMaxPlot = Math.max(1e-5, 1.1 * X1);
    const yMaxPlotOp = Math.max(1e-3, 1.1 * Y1);

    // 创建平衡线数据
    let eqLineX = [];
    let eqLineY = [];
    let yEqMax = 0;

    if (m !== null && m > 0) {
        const xEq = Array.from({length: 100}, (_, i) => i * xMaxPlot / 99);
        const yEq = xEq.map(x => m * x);
        eqLineX = xEq;
        eqLineY = yEq;
        yEqMax = Math.max(...yEq);
    }

    const yMaxPlot = Math.max(yMaxPlotOp, yEqMax) * 1.05;

    // 创建图表数据
    const traces = [
        // 操作线
        {
            x: opLineX,
            y: opLineY,
            type: 'scatter',
            mode: 'lines+markers',
            name: `操作线 (L/V=${formatDisplayValue(calculatedLVRatio)})`,
            line: {color: 'red', width: 2},
            marker: {color: 'red', size: 8}
        }
    ];

    // 添加平衡线
    if (m !== null && m > 0) {
        traces.push({
            x: eqLineX,
            y: eqLineY,
            type: 'scatter',
            mode: 'lines',
            name: `平衡线 (m=${formatDisplayValue(m)})`,
            line: {color: 'blue', width: 2}
        });
    }

    // 添加关键点标注
    traces.push({
        x: [X2, X1],
        y: [Y2, Y1],
        type: 'scatter',
        mode: 'markers+text',
        name: '关键点',
        marker: {color: 'black', size: 10, symbol: 'circle'},
        text: [`顶部 (${formatMolarRatioValue(X2)}, ${formatMolarRatioValue(Y2)})`,
               `底部 (${formatMolarRatioValue(X1)}, ${formatMolarRatioValue(Y1)})`],
        textposition: 'top center',
        showlegend: false
    });

    // 图表布局
    const layout = {
        title: '吸收操作线与平衡线图',
        xaxis: {
            title: '液相摩尔比 X (kmol CO₂/kmol H₂O)',
            range: [0, xMaxPlot],
            showgrid: true,
            exponentformat: 'e',
            showexponent: 'all'
        },
        yaxis: {
            title: '气相摩尔比 Y (kmol CO₂/kmol air)',
            range: [0, yMaxPlot || 0.01],
            showgrid: true,
            exponentformat: 'e',
            showexponent: 'all'
        },
        showlegend: true,
        legend: {
            x: 0.02,
            y: 0.98,
            bgcolor: 'rgba(255, 255, 255, 0.8)'
        },
        font: {
            family: 'Noto Sans SC, Arial, sans-serif',
            size: 12
        },
        width: 600,
        height: 500,
        margin: {
            l: 60,
            r: 30,
            b: 60,
            t: 80,
            pad: 4
        }
    };

    Plotly.newPlot(plotDiv, traces, layout);
}

// --- 事件监听器 ---
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('absorption-calculate-btn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', performAbsorptionCalculations);
    }
});
