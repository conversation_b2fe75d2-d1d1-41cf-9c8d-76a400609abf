<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研方向</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .btn {
            background-color: #0072B2;
            color: white;
            padding: 0.6rem 1.8rem;
            border-radius: 0.375rem;
            font-weight: 500;
            font-size: 1.05rem;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
        }

        .btn:hover {
            background-color: #D55E00;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .research-tag {
            display: inline-block;
            background-color: #e6f7ff;
            color: #0072B2;
            padding: 0.3rem 0.9rem;
            border-radius: 1rem;
            margin-right: 0.6rem;
            margin-bottom: 0.6rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .research-tag:hover {
            background-color: #0072B2;
            color: white;
        }

        .publication-item {
            border-left: 4px solid #0072B2;
            padding: 1rem 1.2rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border-radius: 0 0.5rem 0.5rem 0;
            background-color: rgba(240, 249, 255, 0.5);
        }

        .publication-item:hover {
            border-left-color: #D55E00;
            background-color: rgba(240, 249, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link">首页</a>
                <a href="profile.html" class="nav-link">个人</a>
                <a href="research.html" class="nav-link active">科研</a>
                <a href="teaching.html" class="nav-link">教学</a>
                <a href="members.html" class="nav-link">成员</a>
                <a href="tool.html" class="nav-link">工具</a>
                <a href="downloads.html" class="nav-link">下载</a>
                <a href="research-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <!-- 科研方向内容 -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                科研方向
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="card p-8 mb-10">
            <h2 class="text-2xl font-semibold mb-4 text-[#006699]">木质素利用的挑战与机遇</h2>
            <p class="mb-6">木质素是地球上储量最丰富的可再生芳香族生物质资源，其独特的苯丙烷单元结构使其成为构建未来可持续化学品与先进材料的理想前体。然而，木质素固有的高度交联网络结构、多样的化学键类型以及官能团分布的非均一性，极大地限制了其高效、选择性的解聚与转化。传统的催化方法，如热解、酸碱催化和贵金属催化，往往需要苛刻的反应条件，且存在目标产物选择性低、催化剂易失活等瓶颈，严重阻碍了木质素资源的高值化进程。
                </p>

            <h2 class="text-2xl font-semibold mb-4 text-[#D55E00]">仿生催化新策略</h2>
            <p class="mb-6">为突破上述挑战，本研究从自然界中生物酶高效、专一的催化机制中汲取灵感，致力于开发基于共价有机框架（Covalent Organic Frameworks, COFs）的新一代仿生催化平台。COFs材料凭借其规整有序的纳米孔道、精确可调的拓扑结构、高的比表面积以及卓越的化学与热稳定性，为模拟生物酶的活性中心及催化微环境提供了理想的载体。本研究将通过“酶固定化”与“人工模拟酶”双重策略，为木质素的精准C-O/C-C键断裂与高效定向转化提供系统性的解决方案。</p>

            <!-- 科研方向流程图 -->
            <div class="flex justify-center mb-6">
                <div class="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-lg border border-blue-200 shadow-lg">
                    <img src="assets/images/research.svg" alt="科研方向流程图" class="w-full max-w-4xl h-auto mx-auto rounded-lg shadow-md">
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-8 mt-8">
                <div class="gradient-bg p-6 rounded-lg border-l-4 border-[#0072B2] shadow-md">
                    <h3 class="text-xl font-semibold mb-3 text-[#0072B2]">基于“锚定效应”的酶@COF杂化催化</h3>
                    <p class="mb-4">该策略旨在通过COF的空间限域效应（Spatial Confinement）来稳定并增强天然木质素降解酶。我们将酶分子锚定（Anchor）于COF的规整孔道内，利用其可设计的微环境优化底物取向与传质过程。此举不仅旨在克服天然酶固有的不稳定性，更是为了通过诱导性的构象匹配，显著提升对木质素特定化学键的解聚速率与选择性。</p>
                    <ul class="list-disc list-inside space-y-2">
                        <li>COFs/酶复合催化体系的构筑</li>
                        <li>构效关系与分子动力学模拟</li>
                        <li>催化性能的定向优化与调控</li>
                        <li>底物选择性调控策略的开发</li>
                    </ul>
                </div>

                <div class="gradient-bg p-6 rounded-lg border-l-4 border-[#006699] shadow-md">
                    <h3 class="text-xl font-semibold mb-3 text-[#006699]">基于“极简原则”的COF基人工酶</h3>
                    <p class="mb-4">该策略遵循“极简原则（Minimalism）”，在原子尺度上构筑结构明确的仿生催化剂。我们摒弃天然酶庞大且不稳定的蛋白质骨架，直接在化学性质稳健的COF框架中精确嵌入最高效的核心催化基元（如路易斯酸-布朗斯特酸协同位点）。其核心目标是创制出在稳定性、活性及可循环性方面全面超越天然酶的、可理性设计的新一代“人工酶”。</p>
                    <ul class="list-disc list-inside space-y-2">
                        <li>仿生催化中心的精准构筑</li>
                        <li>催化微环境的理性设计</li>
                        <li>底物识别位点的设计与集成</li>
                        <li>多活性位点的协同催化机制研究</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card p-8 mb-10">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl font-semibold text-[#0072B2]">研究成果</h3>
                <a href="https://www.scholarmate.com/P/eIvume" target="_blank" class="text-[#0072B2] hover:text-[#D55E00] flex items-center ml-6">
                    <i class="fas fa-globe mr-2"></i>科研之友
                </a>
            </div>

            <div class="mb-8">
                <h4 class="font-semibold mb-3 text-[#D55E00] pb-2 border-b border-gray-200">代表性论文</h4>

                <div class="publication-item">
                    <p class="font-semibold">High-Efficiency Dual-Site Biomimetic Catalyst for Lignin Depolymerization</p>
                    <p class="text-sm text-gray-600">ACS Catalysis, 2025, 15 (3), 2595-2606</p>
                    <p class="text-sm">双位点仿生催化剂实现木质素低温解聚（蒋文智）</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">Boosting Catalytic Performance of Cytochrome c through Tailored Carboxymethylation in Covalent Organic Frameworks</p>
                    <p class="text-sm text-gray-600">ACS Catalysis, 2024, 14 (10), 7639-7648</p>
                    <p class="text-sm">定制羧甲基化提升共价有机框架固定化细胞色素c的催化性能（师伦伦）</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">In Situ Encapsulation of Cytochrome c within Covalent Organic Framesworks Using Deep Eutectic Solvents under Ambient Conditions</p>
                    <p class="text-sm text-gray-600">ACS Applied Materials & Interfaces, 2023, 15 (46), 53871-53880</p>
                    <p class="text-sm">环境条件下用深共晶溶剂在共价有机框架内原位包封细胞色素c（李良卫）</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">Anchoring Effect-Induced Conformation Remodeling in Epoxy-Functionalized Covalent Organic Frameworks for Enhanced Enzymatic Efficiency</p>
                    <p class="text-sm text-gray-600"> Langmuir, 2025, 41, 18, 11765–11775</p>
                    <p class="text-sm">锚定效应诱导的环氧功能化共价有机框架中的构象重塑以增强酶催化效率（卢颐康）</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">Harnessing Copper-Metalated Covalent Organic Frameworks: A Biomimetic Approach to High-Efficiency Dye Degradation</p>
                    <p class="text-sm text-gray-600">ACS Applied Engineering Materials, 2024, 3 (1), 225-232</p>
                    <p class="text-sm">仿生构建铜金属化共价有机框架用于染料降解（詹佳敏）</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">Chlorine Axial Coordination Enables Peroxidase Mimicking and Lignin Depolymerization in Fe–N3O Single-Atom Nanozymes</p>
                    <p class="text-sm text-gray-600">ACS Applied Materials & Interfaces, 2025, 17 (30), 43378–43389</p>
                    <p class="text-sm">氯轴向配位增强Fe-N₃O单原子纳米酶拟酶活性与木质素解聚（李奇峰）</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">Biomimetic Dual-Coordination-Sphere Porphyrin-Based Covalent Organic Frameworks Enable Efficient and Selective Furfural Oxidation</p>
                    <p class="text-sm text-gray-600">ACS Applied Materials & Interfaces, 2025, 17 (36), 51043–51052</p>
                    <p class="text-sm">仿生双配位层卟啉COF实现高效选择性糠醛氧化（詹佳敏）</p>
                </div>
            </div>

            <div class="mb-8">
                <h4 class="font-semibold mb-3 text-[#D55E00] pb-2 border-b border-gray-200">专利</h4>

                <div class="publication-item">
                    <p class="font-semibold">一种基于环氧基化改性共价有机框架载体的固定化酶催化剂及其制备方法</p>
                    <p class="text-sm text-gray-600">中国发明专利，申请号：CN202411320749.4</p>
                </div>


                <div class="publication-item">
                    <p class="font-semibold">一种基于羧基化改性共价有机框架载体的固定化酶催化剂及其制备方法</p>
                    <p class="text-sm text-gray-600">中国发明专利，申请号：CN202310666094.5</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">一种人工双位点仿生多酶催化剂的制备方法及其木质素降解应用</p>
                    <p class="text-sm text-gray-600">中国发明专利，申请号：CN202411239367.9</p>
                </div>
                </div>
       </div>

        <div class="card p-8 mb-10">
            <h3 class="text-2xl font-semibold mb-4 text-[#0072B2]">科研项目</h3>

            <div class="publication-item">
                <p class="font-semibold">基于共价有机框架的人工酶设计与构建</p>
                <p class="text-sm text-gray-600">广东省自然科学基金面上项目，主持，2025-2027</p>
            </div>

            <div class="publication-item">
                <p class="font-semibold">酶-化学协同催化体系的设计、构建及其作用机制研究</p>
                <p class="text-sm text-gray-600">国家自然科学基金青年项目，主持，已结题</p>
            </div>

            <div class="publication-item">
                <p class="font-semibold">人工载体多酶组装体系构建与调控机制</p>
                <p class="text-sm text-gray-600">国家重点研发计划，参与，已结题</p>
            </div>
        </div>


    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // 基本的页面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
            console.log('科研页面加载完成');

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
