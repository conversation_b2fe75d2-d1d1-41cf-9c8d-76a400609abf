<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学工作</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.4), transparent);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.02), rgba(0, 102, 153, 0.02));
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .card:hover::before {
            opacity: 1;
            height: 3px;
        }

        .card:hover::after {
            opacity: 1;
        }

        .btn {
            background-color: #0072B2;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
        }

        .btn:hover {
            background-color: #D55E00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .publication-item {
            border-left: 4px solid #0072B2;
            padding: 1rem 1.2rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border-radius: 0 0.5rem 0.5rem 0;
            background-color: rgba(240, 249, 255, 0.5);
        }

        .publication-item:hover {
            border-left-color: #D55E00;
            background-color: rgba(240, 249, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link">首页</a>
                <a href="profile.html" class="nav-link">个人</a>
                <a href="research.html" class="nav-link">科研</a>
                <a href="teaching.html" class="nav-link active">教学</a>
                <a href="members.html" class="nav-link">成员</a>
                <a href="tool.html" class="nav-link">工具</a>
                <a href="downloads.html" class="nav-link">下载</a>
                <a href="index-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <!-- 教学工作内容 -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                教学工作
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="card p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">化工原理课程(中文/全英)</h2>
            <p class="mb-6">《化工原理》(Principles of Chemical Engineering) 是化学工程、环境工程、生物工程等化学相关专业的一门核心基础课程。本课程致力于系统阐述流体流动、传热及传质分离等核心单元操作的基本原理，并讲授相关的工程设计与计算方法。

                为满足不同学生的学习需求，本课程平行开设全中文授课与全英文授课两种班型。课程注重理论知识与工程实践的紧密结合，旨在培养学生扎实的专业理论基础和分析与解决复杂工程问题的能力，为其后续专业课程学习及未来职业发展奠定坚实基础。</p>

            <div class="grid md:grid-cols-2 gap-8 mt-8">
                <div class="bg-blue-50 p-6 rounded-lg border-l-4 border-[#0072B2]">
                    <h3 class="text-xl font-semibold mb-3 text-[#0072B2]">流体力学与传热 Fluid Mechanics and Heat Transfer
                    </h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>流体流动 Fluid Flow </li>
                        <li>流体输送机械 Fluid Transportation Machinery</li>
                        <li>非均相物系分离 Heterogeneous Separation
                        </li>
                        <li>传热与换热设备 Heat Transfer and Heat Exchange Equipment </li>
                    </ul>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg border-l-4 border-[#0072B2]">
                    <h3 class="text-xl font-semibold mb-3 text-[#0072B2]">传质与分离工程 Mass Transfer and Separation Processes
                    </h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>蒸馏 Distillation</li>
                        <li>吸收 Gas Absorption </li>
                        <li>干燥 Drying </li>
                        <li>塔设备 Column  equipment</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card p-6 mb-8">
            <div class="flex items-start">
                <div class="flex-grow">
                    <h3 class="text-2xl font-semibold mb-4 text-[#D55E00]">生成式AI赋能化工原理教学</h3>
                    <p class="mb-6">通过大语言模型辅助Python编程解决精馏等实际问题，实现过程可视化与高效计算，有效克服了概念抽象、计算繁琐及理论脱节等传统痛点，显著深化了学生对原理的理解。这一新范式不仅提升了学生的AI应用与编程能力，更致力于贯通理论学习、化工实验与课程设计，并引入机器学习驱动科研探索，从而构建教学、科研、应用深度融合的创新育人格局。</p>

                    <!-- AI4E流程图 -->
                    <div class="flex justify-center mb-6">
                        <div class="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-lg border border-blue-200 shadow-lg">
                            <img src="assets/images/AI4E.svg" alt="生成式AI赋能编程化工计算流程图" class="w-full max-w-4xl h-auto mx-auto rounded-lg shadow-md">
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-[#0072B2]">
                            <h4 class="font-semibold mb-2">AI辅助教学的优势</h4>
                            <ul class="list-disc list-inside space-y-1">
                                <li>个性化的学习体验</li>
                                <li>即时的反馈与解答</li>
                                <li>提高学生的参与度</li>
                                <li>增强复杂概念理解</li>
                            </ul>
                        </div>

                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-[#0072B2]">
                            <h4 class="font-semibold mb-2">AI工具应用场景</h4>
                            <ul class="list-disc list-inside space-y-1">
                                <li>化工原理知识强化理解 </li>
                                <li>单元操作过程计算分析</li>
                                <li>数据集产生与机器学习</li>
                                <li>化学工程课程设计辅助</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <a href="tool.html" class="btn">
                            <i class="fas fa-calculator mr-2"></i> 体验化工计算工具
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 机器学习赋能精馏过程预测分析 -->
        <div class="card p-6 mb-8">
            <div class="flex items-start">
                <div class="flex-grow">
                    <h3 class="text-2xl font-semibold mb-4 text-[#D55E00]">机器学习赋能化工精馏教学</h3>
                    <p class="mb-6">通过深度融合机器学习方法，革新传统精馏过程的教学模式，旨在培养学生数据驱动的认知能力与自主科研创新素养。该方法引导学生从被动知识接受者转变为主动探索者，要求其独立完成从构建符合研究需求的高质量数据集，到自主选择、训练、评估并对比多种机器学习模型的全过程。</p>

                    <!-- 机器学习流程图 -->
                    <div class="flex justify-center mb-6">
                        <div class="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-lg border border-blue-200 shadow-lg">
                            <img src="assets/images/ML.svg" alt="机器学习赋能精馏过程预测分析流程图" class="w-full max-w-4xl h-auto mx-auto rounded-lg shadow-md">
                        </div>
                    </div>

                    <!-- 技术特点和应用场景 -->
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-[#0072B2]">
                            <h4 class="font-semibold mb-2">核心技术</h4>
                            <ul class="list-disc list-inside space-y-1 text-sm">
                                <li>神经网络回归预测</li>
                                <li>随机森林特征重要性分析</li>
                                <li>支持向量机非线性建模</li>
                                <li>集成学习提升预测精度</li>
                            </ul>
                        </div>

                        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-[#22c55e]">
                            <h4 class="font-semibold mb-2">应用场景</h4>
                            <ul class="list-disc list-inside space-y-1 text-sm">
                                <li>精馏塔分离效率预测</li>
                                <li>操作参数优化建议</li>
                                <li>能耗预测与节能分析</li>
                                <li>工艺设计的优化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <a href="downloads.html" class="btn" style="background-color: #22c55e;">
                            <i class="fas fa-download mr-2"></i> 下载机器学习资料
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card p-6 mb-8">
            <h3 class="text-2xl font-semibold mb-4 text-[#0072B2]">课程团队</h3>
            <div class="grid md:grid-cols-2 gap-6">
                <div class="gradient-bg p-4 rounded-lg shadow-md">
                    <h4 class="font-semibold mb-2 text-center">国家级线下一流课程</h4>
                    <p class="text-center text-gray-700">《化工原理》课程获评国家级线下一流课程</p>
                </div>

                <div class="gradient-bg p-4 rounded-lg shadow-md">
                    <h4 class="font-semibold mb-2 text-center">全国优秀教学团队</h4>
                    <p class="text-center text-gray-700">全国石油和化工行业优秀教学团队骨干教师</p>
                </div>
            </div>
        </div>

        <div class="card p-6 mb-8">
            <h3 class="text-2xl font-semibold mb-4 text-[#0072B2]">教学研究论文</h3>

            <div class="mb-8">
                <h4 class="font-semibold mb-3 text-[#D55E00] pb-2 border-b border-gray-200">代表性论文</h4>

                <div class="publication-item">
                    <p class="font-semibold">基于3D打印技术的离心泵教学创新与实践探索</p>
                    <p class="text-sm text-gray-600">李致贤, 李奇峰, 刘伟峰. 化工高等教育, 2024, 41(05): 140-144</p>
                    <p class="text-sm">利用3D打印技术制作离心泵教具，提升学生对设备结构和工作原理的理解。</p>
                </div>

                <div class="publication-item">
                    <p class="font-semibold">化工原理实验课程思政教学探索与实践</p>
                    <p class="text-sm text-gray-600">李致贤, 黄锦浩, 郑大锋. 广州化工, 2022, 50(21): 203-205</p>
                    <p class="text-sm">将课程思政元素融入化工原理实验教学，培养学生家国情怀和工程伦理意识。</p>
                </div>
            </div>
        </div>
    </main>


    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // 基本的页面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
            console.log('教学页面加载完成');

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
