<svg width="820" height="545" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="clip0"><rect x="237" y="158" width="820" height="545"/></clipPath><linearGradient x1="174.25" y1="152.695" x2="358.75" y2="152.695" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill1"><stop offset="0" stop-color="#3B5998"/><stop offset="1" stop-color="#8B9DC3"/></linearGradient><linearGradient x1="471.054" y1="152.695" x2="655.554" y2="152.695" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill2"><stop offset="0" stop-color="#3B5998"/><stop offset="1" stop-color="#8B9DC3"/></linearGradient><clipPath id="clip3"><rect x="688" y="257" width="16" height="11"/></clipPath><clipPath id="clip4"><rect x="785" y="337" width="11" height="17"/></clipPath><linearGradient x1="174.25" y1="275.695" x2="358.75" y2="275.695" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill5"><stop offset="0" stop-color="#0072B2"/><stop offset="1" stop-color="#56B4E9"/></linearGradient><linearGradient x1="471.946" y1="275.695" x2="656.446" y2="275.695" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill6"><stop offset="0" stop-color="#0072B2"/><stop offset="1" stop-color="#56B4E9"/></linearGradient><clipPath id="clip7"><rect x="688" y="380" width="16" height="12"/></clipPath><linearGradient x1="174.25" y1="408.945" x2="358.75" y2="408.945" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill8"><stop offset="0" stop-color="#009E73"/><stop offset="1" stop-color="#7FC97F"/></linearGradient><linearGradient x1="471.946" y1="408.945" x2="656.446" y2="408.945" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill9"><stop offset="0" stop-color="#009E73"/><stop offset="1" stop-color="#7FC97F"/></linearGradient><clipPath id="clip10"><rect x="786.01" y="467.547" width="10.7625" height="15.4031"/></clipPath><clipPath id="clip11"><rect x="688" y="513" width="16" height="12"/></clipPath><linearGradient x1="225.5" y1="542.195" x2="594.5" y2="542.195" gradientUnits="userSpaceOnUse" spreadMethod="pad" id="fill12"><stop offset="0" stop-color="#CC79A7"/><stop offset="1" stop-color="#E69F00"/></linearGradient><clipPath id="clip13"><rect x="785" y="602" width="11" height="17"/></clipPath></defs><g clip-path="url(#clip0)" transform="translate(-237 -158)"><text fill="#333333" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="26" transform="matrix(1.025 0 0 1.02688 433.8 142)">精馏塔理论塔板数预测机器学习流程</text><path d="M688.8 96.32C693.329 96.32 697 99.9912 697 104.52L697 200.87C697 205.399 693.329 209.07 688.8 209.07L131.2 209.07C126.671 209.07 123 205.399 123 200.87L123 104.52C123 99.9912 126.671 96.32 131.2 96.32Z" stroke="#DDDDDD" stroke-width="1.5375" fill="#F9F9F9" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#333333" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="20" transform="matrix(1.025 0 0 1.02688 565 209)">阶段一：数据生成<tspan font-size="14" x="-150" y="0">输入特征</tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="14" x="-94" y="0">:</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-150" y="20.4504">R</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-141" y="20.4504">, </tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-135" y="20.4504">TF</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-121" y="20.4504">, </tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-115" y="20.4504">xF</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-103" y="20.4504">, </tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-97" y="20.4504">xD</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-83" y="20.4504">, </tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="-77" y="20.4504">xW</tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="14" x="315.565" y="0">:</tspan><tspan font-size="14" x="259.565" y="0">输出目标</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="262.565" y="20.4504">theoretical</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="255.565" y="20.4504">_</tspan><tspan fill="#555555" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" x="246.565" y="20.4504">N</tspan></text><path d="M352.6 152.695C355.997 152.695 358.75 155.448 358.75 158.845L358.75 192.67C358.75 196.067 355.997 198.82 352.6 198.82L180.4 198.82C177.003 198.82 174.25 196.067 174.25 192.67L174.25 158.845C174.25 155.448 177.003 152.695 180.4 152.695Z" stroke="#3B5998" stroke-width="1.5375" fill="url(#fill1)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 424.062 268)">McCabe-Thiele<tspan font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-size="16" x="107" y="0">法模拟</tspan></text><path d="M649.404 152.695C652.801 152.695 655.554 155.448 655.554 158.845L655.554 192.67C655.554 196.067 652.801 198.82 649.404 198.82L477.204 198.82C473.808 198.82 471.054 196.067 471.054 192.67L471.054 158.845C471.054 155.448 473.808 152.695 477.204 152.695Z" stroke="#3B5998" stroke-width="1.5375" fill="url(#fill2)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 743.083 268)">批量数据生成</text><path d="M358.75 175.245 451 175.245" stroke="#555555" stroke-width="1.5375" fill="none" transform="matrix(1 0 0 1.00183 237 86.8745)"/><g clip-path="url(#clip3)"><path d="M0 0 15.375 5.38125 0 10.7625Z" fill="#555555" transform="matrix(1 0 0 1.00183 688 257.049)"/></g><path d="M688.8 229.57C693.329 229.57 697 233.241 697 237.77L697 334.12C697 338.649 693.329 342.32 688.8 342.32L131.2 342.32C126.671 342.32 123 338.649 123 334.12L123 237.77C123 233.241 126.671 229.57 131.2 229.57Z" stroke="#DDDDDD" stroke-width="1.5375" fill="#F9F9F9" transform="matrix(1 0 0 1.00183 237 86.8745)"/><path d="M553.5 206.396 553.5 250.293" stroke="#555555" stroke-width="1.5375" fill="none" transform="matrix(1 0 0 1.00183 237 86.8745)"/><g clip-path="url(#clip4)"><path d="M0 0 15.375 5.38125 0 10.7625Z" fill="#555555" transform="matrix(6.12323e-17 1.00183 -1 6.13444e-17 795.881 337.625)"/></g><text fill="#333333" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="20" transform="matrix(1.025 0 0 1.02688 554.75 343)">阶段二：数据预处理</text><path d="M352.6 275.695C355.997 275.695 358.75 278.448 358.75 281.845L358.75 315.67C358.75 319.067 355.997 321.82 352.6 321.82L180.4 321.82C177.003 321.82 174.25 319.067 174.25 315.67L174.25 281.845C174.25 278.448 177.003 275.695 180.4 275.695Z" stroke="#0072B2" stroke-width="1.5375" fill="url(#fill5)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 429.7 391)">数据清洗与特征缩放</text><path d="M650.296 275.695C653.692 275.695 656.446 278.448 656.446 281.845L656.446 315.67C656.446 319.067 653.692 321.82 650.296 321.82L478.096 321.82C474.699 321.82 471.946 319.067 471.946 315.67L471.946 281.845C471.946 278.448 474.699 275.695 478.096 275.695Z" stroke="#0072B2" stroke-width="1.5375" fill="url(#fill6)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 725.613 391)">训练集与测试集划分</text><path d="M358.75 298.245 451 298.245" stroke="#555555" stroke-width="1.5375" fill="none" transform="matrix(1 0 0 1.00183 237 86.8745)"/><g clip-path="url(#clip7)"><path d="M0 0 15.375 5.38125 0 10.7625Z" fill="#555555" transform="matrix(1 0 0 1.00183 688 380.274)"/></g><path d="M688.8 362.82C693.329 362.82 697 366.491 697 371.02L697 467.37C697 471.899 693.329 475.57 688.8 475.57L131.2 475.57C126.671 475.57 123 471.899 123 467.37L123 371.02C123 366.491 126.671 362.82 131.2 362.82Z" stroke="#DDDDDD" stroke-width="1.5375" fill="#F9F9F9" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#333333" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="20" transform="matrix(1.025 0 0 1.02688 534.25 476)">阶段三：模型训练与评估</text><path d="M352.6 408.945C355.997 408.945 358.75 411.698 358.75 415.095L358.75 448.92C358.75 452.317 355.997 455.07 352.6 455.07L180.4 455.07C177.003 455.07 174.25 452.317 174.25 448.92L174.25 415.095C174.25 411.698 177.003 408.945 180.4 408.945Z" stroke="#009E73" stroke-width="1.5375" fill="url(#fill8)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 435.226 517)">多模型训练与比较</text><path d="M650.296 408.945C653.692 408.945 656.446 411.698 656.446 415.095L656.446 448.92C656.446 452.317 653.692 455.07 650.296 455.07L478.096 455.07C474.699 455.07 471.946 452.317 471.946 448.92L471.946 415.095C471.946 411.698 474.699 408.945 478.096 408.945Z" stroke="#009E73" stroke-width="1.5375" fill="url(#fill9)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 745.757 516)">模型性能评估</text><path d="M554.391 330.287 554.391 379.978" stroke="#555555" stroke-width="1.5375" fill="none" transform="matrix(1 4.2394e-07 -4.23166e-07 1.00183 237 86.8742)"/><g clip-path="url(#clip10)"><path d="M0 0 15.375 5.38125 0 10.7625Z" fill="#555555" transform="matrix(-4.23166e-07 1.00183 -1 -4.2394e-07 796.773 467.547)"/></g><path d="M358.75 431.495 451 431.495" stroke="#555555" stroke-width="1.5375" fill="none" transform="matrix(1 0 0 1.00183 237 86.8745)"/><g clip-path="url(#clip11)"><path d="M0 0 15.375 5.38125 0 10.7625Z" fill="#555555" transform="matrix(1 0 0 1.00183 688 513.768)"/></g><path d="M688.8 496.07C693.329 496.07 697 499.741 697 504.27L697 580.12C697 584.649 693.329 588.32 688.8 588.32L131.2 588.32C126.671 588.32 123 584.649 123 580.12L123 504.27C123 499.741 126.671 496.07 131.2 496.07Z" stroke="#DDDDDD" stroke-width="1.5375" fill="#F9F9F9" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#333333" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="20" transform="matrix(1.025 0 0 1.02688 565 610)">阶段四：模型应用</text><path d="M588.35 542.195C591.747 542.195 594.5 544.948 594.5 548.345L594.5 571.92C594.5 575.317 591.747 578.07 588.35 578.07L231.65 578.07C228.253 578.07 225.5 575.317 225.5 571.92L225.5 548.345C225.5 544.948 228.253 542.195 231.65 542.195Z" stroke="#CC79A7" stroke-width="1.5375" fill="url(#fill12)" transform="matrix(1 0 0 1.00183 237 86.8745)"/><text fill="#FFFFFF" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="16" transform="matrix(1.025 0 0 1.02688 524 654)">预测新工况下的精馏塔理论塔板数</text><path d="M553.5 466.211 553.5 515.01" stroke="#555555" stroke-width="1.5375" fill="none" transform="matrix(1 0 0 1.00183 237 86.8745)"/><g clip-path="url(#clip13)"><path d="M0 0 15.375 5.38125 0 10.7625Z" fill="#555555" transform="matrix(6.12323e-17 1.00183 -1 6.13444e-17 795.881 602.827)"/></g><text fill="#666666" font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(1.025 0 0 1.02688 439.95 536)">线性回归<tspan font-size="12" x="48" y="0">、</tspan><tspan font-size="12" x="60" y="0">随机森林等</tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="12" x="303.109" y="0.973828">R</tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="12" x="312.109" y="0.973828">²</tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="12" x="316.109" y="0.973828">, </tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="12" x="322.109" y="0.973828">RMSE</tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="12" x="356.109" y="0.973828">, </tspan><tspan font-family="Arial,Arial_MSFontService,sans-serif" font-size="12" x="362.109" y="0.973828">MAE</tspan></text></g></svg>