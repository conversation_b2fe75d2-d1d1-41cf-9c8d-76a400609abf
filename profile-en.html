<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Principal Investigator</title>
    <!-- Page version: v1.1.1 - Updated: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header navigation styles */
        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .research-tag {
            display: inline-block;
            background: linear-gradient(135deg, var(--light-blue), #dbeafe);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin-right: 0.75rem;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(0, 114, 178, 0.1);
            cursor: pointer;
        }

        .research-tag:hover {
            background: linear-gradient(135deg, var(--primary-blue), #0056b3);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-blue);
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index-en.html" class="nav-link">Home</a>
                <a href="profile-en.html" class="nav-link active">Profile</a>
                <a href="research-en.html" class="nav-link">Research</a>
                <a href="members-en.html" class="nav-link">Members</a>
                <a href="profile.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>中文
                </a>
            </div>
        </div>
    </header>

    <!-- Profile Content -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                Team Leader
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <div class="md:col-span-1">
                <div class="card p-6 text-center">
                    <div class="w-48 h-48 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/李致贤照片.jpg" alt="Li Zhixian Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/200?text=Photo'">
                    </div>
                    <h3 class="text-3xl text-[#006699] font-semibold mb-2">Li Zhixian</h3>
                    <p class="text-3xl-gray-600 mb-4">Associate Professor</p>
                    <p class="text-3xl-gray-600 mb-4">School of Chemistry and Chemical Engineering</p>
                    <p class="text-3xl-gray-600 mb-4">South China University of Technology</p>

                    <div class="flex justify-center items-center space-x-6 mt-2">
                        <a href="https://www.scholarmate.com/P/eIvume" target="_blank" class="text-[#0072B2] hover:text-[#D55E00] flex items-center"><i class="fas fa-globe mr-2"></i>ScholarMate</a>
                        <a href="https://scholar.google.com/citations?user=Bbv7634AAAAJ&hl=zh-CN" target="_blank" class="text-[#0072B2] hover:text-[#D55E00] flex items-center"><i class="fab fa-google mr-2"></i>Google Scholar</a>
                    </div>
                </div>
            </div>
            <div class="md:col-span-2">
                <div class="card p-6">
                    <h3 class="text-2xl font-semibold mb-4 text-[#0072B2]">Biography</h3>
                    <ul class="list-disc list-inside space-y-3 mb-6">
                        <li> Long-term commitment to biomimetic design and chemical modification research of enzymatic catalytic systems, focusing on key scientific issues in lignocellulosic bioconversion, providing theoretical foundation and technical support for sustainable biomanufacturing.</li>
                        <li>As project leader, presided over vertical research projects including National Natural Science Foundation and sub-projects of National Key R&D Program. Research achievements have been published 56 SCI papers in internationally renowned journals such as ACS Catalysis, and authorized more than 9 Chinese invention patents and 2 US patents.</li>
                        <li>Serves as a lecturer for national-level offline first-class courses and is a core member of the National Excellent Teaching Team in the petroleum and chemical industry. Undertakes undergraduate core course "Chemical Engineering Principles" (Chinese and full English) teaching tasks, with annual teaching hours exceeding 300. Published 2 teaching research papers in professional journals such as "Chemical Higher Education" and participated in writing 1 professional textbook.</li>
                    </ul>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">Education</h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>2010.09-2016.01, Tsinghua University, Ph.D. in Chemical Engineering and Technology, Supervisors: Ouyang Pingkai, Liu Zheng</li>
                        <li>2006.09-2010.07, Nanjing Tech University, B.S. in Pharmaceutical Engineering, Class Advisor/Supervisor: Huang He</li>
                    </ul>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">Work Experience</h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>2021.09 - Present, Associate Professor, School of Chemistry and Chemical Engineering, South China University of Technology</li>
                        <li>2018.09 - 2021.09, Lecturer, School of Chemistry and Chemical Engineering, South China University of Technology</li>
                        <li>2016.06 - 2018.09, Postdoctoral Fellow, School of Chemistry and Chemical Engineering, South China University of Technology</li>
                    </ul>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">Research Interests</h3>
                    <div class="flex flex-wrap mb-4">
                        <span class="research-tag">Lignin Utilization</span>
                        <span class="research-tag">Biomimetic Catalysis</span>
                        <span class="research-tag">Enzymatic Catalysis</span>
                        <span class="research-tag">Covalent Organic Frameworks</span>
                    </div>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">Affiliated Teams</h3>
                    <p class="mb-2">Biomass Chemical Engineering Team <a class="text-[#0072B2] hover:text-[#D55E00]">(Leader: Qiu Xueqing)</a></p>
                    <p class="mb-2">Chemical Engineering Principles Teaching Team <a class="text-[#0072B2] hover:text-[#D55E00]">(Leader: Zheng Dafeng)</a></p>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">Contact Information</h3>
                    <p class="mb-2">Email: <a class="text-[#0072B2] hover:text-[#D55E00]"><EMAIL></a></p>
                    <p>Office Address: Building 16, School of Chemistry and Chemical Engineering, Wushan Campus, South China University of Technology</p>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme Insights. All Rights Reserved.</p>
            <p class="text-gray-300 text-lg">This website content is for research and teaching sharing only, without any commercial purpose. ICP No. 2025423918.</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // Basic page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Page load completion initialization
            console.log('English profile page loaded');

            // Add version number to page to prevent cache issues
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
