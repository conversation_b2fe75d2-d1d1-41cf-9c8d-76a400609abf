<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Research Group Members</title>
    <!-- Page version: v1.1.1 - Updated: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header navigation styles */
        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .btn {
            background-color: #0072B2;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
            text-decoration: none;
            text-align: center;
            border: none;
            outline: none;
        }

        .btn:hover {
            background-color: #D55E00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index-en.html" class="nav-link">Home</a>
                <a href="profile-en.html" class="nav-link">Profile</a>
                <a href="research-en.html" class="nav-link">Research</a>
                <a href="members-en.html" class="nav-link active">Members</a>
                <a href="members.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>中文
                </a>
            </div>
        </div>
    </header>

    <!-- Research Group Members Content -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                 Team Members
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="mb-6 text-lg text-center">
            <p>Join COFzyme, create a new era of high-value biomass utilization together! 🌍✨</p>
        </div>

        <!-- Current Graduate Students -->
        <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-6 text-[#0072B2] text-center">Current Graduate Students (11 people)</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Zhan Jiamin -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/詹佳敏.jpg" alt="Zhan Jiamin Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Zhan+Jiamin'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Zhan Jiamin</h3>
                    <p class="text-lg text-gray-600 mb-2">2023 Master's Student</p>
                    <p class="text-lg text-gray-500">COF Artificial Enzyme</p>
                </div>

                <!-- Lu Haosheng -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/卢浩盛.jpg" alt="Lu Haosheng Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Lu+Haosheng'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Lu Haosheng</h3>
                    <p class="text-lg text-gray-600 mb-2">2023 Master's Student (Soldier Program)</p>
                    <p class="text-lg text-gray-500">COF Artificial Enzyme</p>
                </div>

                <!-- Lu Yikang -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/卢颐康.jpg" alt="Lu Yikang Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Lu+Yikang'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Lu Yikang</h3>
                    <p class="text-lg text-gray-600 mb-2">2023 Master's Student</p>
                    <p class="text-lg text-gray-500">COF Immobilized Enzyme</p>
                </div>

                <!-- Dai Huajie -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/戴华杰.jpg" alt="Dai Huajie Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Dai+Huajie'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Dai Huajie</h3>
                    <p class="text-lg text-gray-600 mb-2">2024 Master's Student</p>
                    <p class="text-lg text-gray-500">COF Artificial Enzyme</p>
                </div>

                <!-- Pan Yang -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/潘杨.jpg" alt="Pan Yang Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Pan+Yang'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Pan Yang</h3>
                    <p class="text-lg text-gray-600 mb-2">2024 Master's Student</p>
                    <p class="text-lg text-gray-500">COF Immobilized Enzyme</p>
                </div>

                <!-- Mai Weijian -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/麦伟健.jpg" alt="Mai Weijian Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Mai+Weijian'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Mai Weijian</h3>
                    <p class="text-lg text-gray-600 mb-2">2024 Master's Student</p>
                    <p class="text-lg text-gray-500">COF Artificial Enzyme</p>
                </div>

                <!-- Zeng Huimei -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/曾惠梅.jpg" alt="Zeng Huimei Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Zeng+Huimei'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Zeng Huimei</h3>
                    <p class="text-lg text-gray-600 mb-2">2025 Master's Student</p>
                    <p class="text-lg text-gray-500">Research Direction: TBD</p>
                </div>

                <!-- Tao Changcheng -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/陶长成.jpg" alt="Tao Changcheng Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Tao+Changcheng'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Tao Changcheng</h3>
                    <p class="text-lg text-gray-600 mb-2">2025 Master's Student</p>
                    <p class="text-lg text-gray-500">Research Direction: TBD</p>
                </div>

                <!-- Yin Zheng -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/尹正.jpg?v=1.1.1" alt="Yin Zheng Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Yin+Zheng'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Yin Zheng</h3>
                    <p class="text-lg text-gray-600 mb-2">2026 Master's Student</p>
                    <p class="text-lg text-gray-500">Research Direction: TBD</p>
                </div>

                <!-- Huang Jiayi -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/黄嘉怡.jpg?v=1.1.1" alt="Huang Jiayi Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Huang+Jiayi'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Huang Jiayi</h3>
                    <p class="text-lg text-gray-600 mb-2">2026 Master's Student</p>
                    <p class="text-lg text-gray-500">Research Direction: TBD</p>
                </div>

                <!-- Xiao Ruqi -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/肖如琪.jpg?v=1.1.1" alt="Xiao Ruqi Photo" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=Xiao+Ruqi'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">Xiao Ruqi</h3>
                    <p class="text-lg text-gray-600 mb-2">2026 Master's Student</p>
                    <p class="text-lg text-gray-500">Research Direction: TBD</p>
                </div>
            </div>
        </div>

        <!-- Graduated Students -->
        <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-6 text-[#0072B2] text-center">Graduated Students (16 people)</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Jiang Wenzhi -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Jiang Wenzhi</h3>
                </div>

                <!-- Li Qifeng -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Li Qifeng</h3>
                </div>

                <!-- Liu Jiankang -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Liu Jiankang</h3>
                </div>

                <!-- Shi Lunlun -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Shi Lunlun</h3>
                </div>

                <!-- Li Liangwei -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Li Liangwei (Soldier Program)</h3>
                </div>

                <!-- Zhang Yingchun -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Zhang Yingchun</h3>
                </div>

                <!-- Yan Caihua -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Yan Caihua</h3>
                </div>

                <!-- Tan Haicheng -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Tan Haicheng</h3>
                </div>

                <!-- Wang Yuanyuan -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Wang Yuanyuan</h3>
                </div>

                <!-- Wang Peng -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Wang Peng</h3>
                </div>

                <!-- Xie Yuanxiang -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Xie Yuanxiang</h3>
                </div>

                <!-- Wang Yalin -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Wang Yalin</h3>
                </div>

                <!-- Zhong Xue -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Zhong Xue</h3>
                </div>

                <!-- Xia Huan -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Xia Huan</h3>
                </div>

                <!-- Pang Jiafeng -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Pang Jiafeng</h3>
                </div>

                <!-- Li Shaomin -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">Li Shaomin</h3>
                </div>
            </div>
        </div>

        <!-- Join Us -->
        <div class="card p-8 mb-10">
            <div class="text-center">
                <h3 class="text-xl font-semibold mb-4 text-[#0072B2]">Join Us</h3>
                <span class="btn">
                    <i class="fas fa-envelope mr-2"></i>Contact us: <EMAIL>
                </span>
            </div>
        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme Insights. All Rights Reserved.</p>
            <p class="text-gray-300 text-lg">This website content is for research and teaching sharing only, without any commercial purpose. ICP No. 2025423918.</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // Basic page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Page load completion initialization
            console.log('English members page loaded');

            // Add version number to page to prevent cache issues
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
