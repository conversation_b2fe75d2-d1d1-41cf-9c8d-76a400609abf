var _0x9a26e=(463350^463346)+(952469^952477);const x_ethanol_data=[169695^169695,0.0201,0.0507,0.0795,0.1048,0.1495,0.2000,0.2500,0.3001,0.3509,0.4000,0.4541,0.5016,0.5400,0.5955,0.6405,0.7063,0.7599,0.7982,0.8597,0.8941,551372^551373];_0x9a26e=(810098^810099)+(257258^257260);var _0xd1377b=(832873^832864)+(732308^732310);const y_ethanol_data=[199537^199537,0.1838,0.3306,0.4018,0.4461,0.4977,0.5309,0.5548,0.5770,0.5955,0.6144,0.6343,0.6534,0.6692,0.6959,0.7186,0.7582,0.7926,0.8183,0.8640,0.8941,173680^173681];_0xd1377b="npfffl".split("").reverse().join("");const x_Tx_data=[499362^499362,0.05,0.1,0.15,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.894,0.95,800465^800464];var _0xd1b16a=(457453^457450)+(418900^418909);const T_Tx_data=[469172^469200,110330^110243,84.1,82.3,81.7,80.4,79.5,78.9,78.5,78.3,78.2,78.15,78.2,78.3];_0xd1b16a=(895094^895092)+(830703^830698);const R_LATENT_HEAT_ETHANOL_KJ_KMOL=616874^655114;var _0x_0x4af;const R_LATENT_HEAT_WATER_KJ_KMOL=724584^757922;_0x_0x4af=(702078^702076)+(192806^192814);const CP_LIQ_ETHANOL_KJ_KMOL_C=260942^260926;const CP_LIQ_WATER_KJ_KMOL_C=75.4;function getBubblePointTemp(xF_mol){if(!(x_Tx_data[182801^182801]<=xF_mol&&xF_mol<=x_Tx_data[x_Tx_data['\u006C\u0065\u006E\u0067\u0074\u0068']-(682287^682286)])){console['\u0077\u0061\u0072\u006E'](`警告: 进料组成 xF=${xF_mol['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](364435^364432)} 超出 T-x 数据范围 [${x_Tx_data[130359^130359]['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](663380^663383)}, ${x_Tx_data[x_Tx_data['\u006C\u0065\u006E\u0067\u0074\u0068']-(983509^983508)]['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](820255^820252)}] 用于泡点计算。结果为外插值，可能不准确。`);}let _0xe8d=linearInterpolation(xF_mol,x_Tx_data,T_Tx_data);return _0xe8d;}function getLatentHeatMixture(xF_mol){var _0xe9e1d=(671998^671993)+(258850^258848);const _0x2d_0x5bg=Math['\u006D\u0061\u0078'](442741^442741,Math['\u006D\u0069\u006E'](parseFloat(xF_mol),986465^986464));_0xe9e1d='\u006F\u006F\u0071\u006B\u0062\u006F';const _0x4d8e7d=_0x2d_0x5bg*R_LATENT_HEAT_ETHANOL_KJ_KMOL+((257982^257983)-_0x2d_0x5bg)*R_LATENT_HEAT_WATER_KJ_KMOL;return Math['\u0061\u0062\u0073'](_0x4d8e7d)>1e-9?_0x4d8e7d:1e-9;}function getHeatCapacityMixture(xF_mol){var _0xc79b2e;const _0x4e263f=Math['\u006D\u0061\u0078'](373744^373744,Math['\u006D\u0069\u006E'](parseFloat(xF_mol),718689^718688));_0xc79b2e=476378^476379;const _0x8bc60b=_0x4e263f*CP_LIQ_ETHANOL_KJ_KMOL_C+((520660^520661)-_0x4e263f)*CP_LIQ_WATER_KJ_KMOL_C;return _0x8bc60b;}function calculateQValue(xF_mol,t_F){try{xF_mol=parseFloat(xF_mol);t_F=parseFloat(t_F);var _0x95ad2d=(205345^205347)+(838797^838793);const _0x2fd=Math['\u006D\u0061\u0078'](742006^742006,Math['\u006D\u0069\u006E'](xF_mol,287670^287671));_0x95ad2d=272828^272824;if(_0x2fd!==xF_mol){console['\u0077\u0061\u0072\u006E'](`警告: 进料组成 xF=${xF_mol['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](838699^838696)} 超出 [0, 1] 范围，已限制为 ${_0x2fd['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](948434^948433)} 用于 q 值计算。`);xF_mol=_0x2fd;}const _0x284cc=getBubblePointTemp(xF_mol);const _0x11g=0.1;if(Math['\u0061\u0062\u0073'](t_F-_0x284cc)<_0x11g){return 870039^870038;}var _0xad_0xf36=(361578^361576)+(181022^181018);const _0x8a_0x10e=getLatentHeatMixture(xF_mol);_0xad_0xf36=(870214^870212)+(236376^236381);var _0xfd1gf=(178541^178540)+(951075^951083);const _0xd9e=getHeatCapacityMixture(xF_mol);_0xfd1gf=(261178^261180)+(394261^394262);if(Math['\u0061\u0062\u0073'](_0x8a_0x10e)<1e-9){console['\u0065\u0072\u0072\u006F\u0072']("\u3002\u96F6\u8FD1\u63A5\u70ED\u6F5C\u5316\u6C7D\u7684\u5230\u5F97\u7B97\u8BA1\u7269\u5408\u6DF7 :\u8BEF\u9519".split("").reverse().join(""));return NaN;}const q=(633761^633760)+_0xd9e*(_0x284cc-t_F)/_0x8a_0x10e;return q;}catch(e){console['\u0065\u0072\u0072\u006F\u0072'](":\u8BEF\u9519\u503C q \u7B97\u8BA1".split("").reverse().join(""),e);return NaN;}}function equilibriumY(x){return linearInterpolation(x,x_ethanol_data,y_ethanol_data);}function equilibriumX(y){return linearInterpolation(y,y_ethanol_data,x_ethanol_data);}function rectifyingOpLine(x,R,xD){R=parseFloat(R);xD=parseFloat(xD);if(R<=(433011^433011))return NaN;if(Math['\u0061\u0062\u0073'](R+(767081^767080))<1e-9)return NaN;return R/(R+(866011^866010))*x+xD/(R+(167842^167843));}function qLineY(x,q,xF,_0x1f9f2c){q=parseFloat(q);xF=parseFloat(xF);_0x1f9f2c=1e-9;if(Math['\u0061\u0062\u0073'](q-(798179^798178))<_0x1f9f2c){return NaN;}else if(Math['\u0061\u0062\u0073'](q-(641740^641740))<_0x1f9f2c){return xF;}else{const _0x6a92a=q-(898331^898330);if(Math['\u0061\u0062\u0073'](_0x6a92a)<_0x1f9f2c)return NaN;return q/_0x6a92a*x-xF/_0x6a92a;}}function findIntersection(R,xD,q,xF,_0xac_0xb53){R=parseFloat(R);xD=parseFloat(xD);q=parseFloat(q);xF=parseFloat(xF);_0xac_0xb53=1e-9;if(R<=(969679^969679)){console['\u0065\u0072\u0072\u006F\u0072']("\u3002\u70B9\u4EA4\u5230\u627E\u80FD\u624D 0 \u4E8E\u5927\u987B\u5FC5 R \u6BD4\u6D41\u56DE :\u8BEF\u9519".split("").reverse().join(""));return[NaN,NaN];}if(Math['\u0061\u0062\u0073'](R+(739661^739660))<_0xac_0xb53){console['\u0065\u0072\u0072\u006F\u0072']("\u9519\u8BEF\u003A\u0020\u0052\u002B\u0031\u0020\u63A5\u8FD1\u4E8E\u96F6\u3002");return[NaN,NaN];}let _0x9bb0fa,_0xc1c1b;if(Math['\u0061\u0062\u0073'](q-(911050^911051))<_0xac_0xb53){_0x9bb0fa=xF;_0xc1c1b=rectifyingOpLine(_0x9bb0fa,R,xD);}else if(Math['\u0061\u0062\u0073'](q-(888262^888262))<_0xac_0xb53){_0xc1c1b=xF;if(Math['\u0061\u0062\u0073'](R)<_0xac_0xb53){console['\u0065\u0072\u0072\u006F\u0072']("\u3002\u6807\u5750 x \u70B9\u4EA4\u7684\u65F6 0=q \u7B97\u8BA1\u6CD5\u65E0\uFF0C\u96F6\u4E8E\u8FD1\u63A5 R :\u8BEF\u9519".split("").reverse().join(""));return[NaN,NaN];}_0x9bb0fa=(_0xc1c1b*(R+(564509^564508))-xD)/R;}else{const _0xb3c=q-(569493^569492);if(Math['\u0061\u0062\u0073'](_0xb3c)<_0xac_0xb53){console['\u0065\u0072\u0072\u006F\u0072']("\u9519\u8BEF\u003A\u0020\u0071\u0020\u63A5\u8FD1\u0020\u0031\uFF0C\u65E0\u6CD5\u8BA1\u7B97\u0020\u0071\u0020\u7EBF\u53C2\u6570\u3002");return[NaN,NaN];}const _0xee969g=q/_0xb3c;const _0x460bda=R/(R+(175448^175449));const _0x18e58g=_0xee969g-_0x460bda;if(Math['\u0061\u0062\u0073'](_0x18e58g)<_0xac_0xb53){console['\u0077\u0061\u0072\u006E']("\u8B66\u544A\uFF1A\u0071\u0020\u7EBF\u548C\u7CBE\u998F\u6BB5\u64CD\u4F5C\u7EBF\u5E73\u884C\u6216\u63A5\u8FD1\u5E73\u884C\uFF0C\u65E0\u6CD5\u627E\u5230\u552F\u4E00\u4EA4\u70B9\u3002");return[NaN,NaN];}var _0xd_0x944=(643788^643780)+(912254^912255);const _0x8fb32b=xD/(R+(691326^691327));_0xd_0x944=(132625^132631)+(331411^331415);const _0x9f9a5b=xF/_0xb3c;_0x9bb0fa=(_0x8fb32b+_0x9f9a5b)/_0x18e58g;_0xc1c1b=rectifyingOpLine(_0x9bb0fa,R,xD);}_0x9bb0fa=Math['\u006D\u0061\u0078'](194865^194865,Math['\u006D\u0069\u006E'](_0x9bb0fa,744439^744438));_0xc1c1b=Math['\u006D\u0061\u0078'](949429^949429,Math['\u006D\u0069\u006E'](_0xc1c1b,408879^408878));return[_0x9bb0fa,_0xc1c1b];}function strippingOpLineCoeffs(x_intersect,y_intersect,xW,_0x8a0a1c){xW=parseFloat(xW);var _0x2ed43a=(171180^171177)+(154041^154044);_0x8a0a1c=1e-9;_0x2ed43a="dokhjc".split("").reverse().join("");if(isNaN(x_intersect)||isNaN(y_intersect)){return[NaN,NaN];}if(Math['\u0061\u0062\u0073'](x_intersect-xW)<_0x8a0a1c){return[NaN,NaN];}const _0x98fd8g=(y_intersect-xW)/(x_intersect-xW);var _0x529a=(517219^517220)+(402082^402087);const _0xc419e=xW-_0x98fd8g*xW;_0x529a=512318^512318;return[_0x98fd8g,_0xc419e];}function linearInterpolation(x,xArray,yArray){if(x<=xArray[211824^211824])return yArray[449378^449378];if(x>=xArray[xArray['\u006C\u0065\u006E\u0067\u0074\u0068']-(512879^512878)])return yArray[yArray['\u006C\u0065\u006E\u0067\u0074\u0068']-(670991^670990)];for(let i=774708^774708;i<xArray['\u006C\u0065\u006E\u0067\u0074\u0068']-(745945^745944);i++){if(xArray[i]<=x&&x<=xArray[i+(652007^652006)]){const t=(x-xArray[i])/(xArray[i+(239139^239138)]-xArray[i]);return yArray[i]*((756442^756443)-t)+yArray[i+(877895^877894)]*t;}}return NaN;}function plotMcCabeThiele(xD,xW,xF,q,R,_0xec29c,_0xf11d){const _0xdc98e=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006D\u0063\u0063\u0061\u0062\u0065\u002D\u0070\u006C\u006F\u0074");const _0x4605e=Array['\u0066\u0072\u006F\u006D']({"length":101},(_,i)=>i/(549055^549083));const _0xde42g=_0x4605e['\u006D\u0061\u0070'](x=>equilibriumY(x));const[x_intersect,y_intersect]=findIntersection(R,xD,q,xF);const[strip_slope,strip_intercept]=strippingOpLineCoeffs(x_intersect,y_intersect,xW);const _0x4fe=[{'\u0078':[136819^136819,185083^185082],'\u0079':[721098^721098,719357^719356],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':'lines','\u006E\u0061\u006D\u0065':'y = x','\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':'black','\u0077\u0069\u0064\u0074\u0068':1}},{'\u0078':_0x4605e,'\u0079':_0xde42g,'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':"\u5E73\u8861\u7EBF","line":{'\u0063\u006F\u006C\u006F\u0072':'blue',"width":2}},{'\u0078':x_ethanol_data,'\u0079':y_ethanol_data,'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073","name":"\u0056\u004C\u0045\u0020\u6570\u636E\u70B9","marker":{'\u0063\u006F\u006C\u006F\u0072':'blue',"size":6,'\u006F\u0070\u0061\u0063\u0069\u0074\u0079':0.7}},{'\u0078':[x_intersect,xD],'\u0079':[y_intersect,xD],'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':'lines',"name":`精馏段操作线 (R=${R['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](350266^350264)})`,'\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':"\u0072\u0065\u0064",'\u0077\u0069\u0064\u0074\u0068':2}},{'\u0078':[xW,x_intersect],'\u0079':[xW,y_intersect],'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':"\u63D0\u998F\u6BB5\u64CD\u4F5C\u7EBF","line":{'\u0063\u006F\u006C\u006F\u0072':'green',"width":2}},{'\u0078':[xD,xW,xF,x_intersect],'\u0079':[xD,xW,xF,y_intersect],"type":'scatter','\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073",'\u006E\u0061\u006D\u0065':"\u5173\u952E\u70B9\u0020\u0078\u0044",'\u006D\u0061\u0072\u006B\u0065\u0072':{'\u0063\u006F\u006C\u006F\u0072':["der".split("").reverse().join(""),"\u0067\u0072\u0065\u0065\u006E","elprup".split("").reverse().join(""),"\u0062\u006C\u0061\u0063\u006B"],"size":10,'\u0073\u0079\u006D\u0062\u006F\u006C':["\u0063\u0069\u0072\u0063\u006C\u0065","elcric".split("").reverse().join(""),"\u0063\u0069\u0072\u0063\u006C\u0065","nepo-elcric".split("").reverse().join("")]},"hoverinfo":"\u006E\u006F\u006E\u0065"}];var _0x13d68b=(120985^120990)+(791442^791450);_0x13d68b=110642^110643;_0xf11d=1e-9;if(Math['\u0061\u0062\u0073'](q-(743099^743098))<_0xf11d){_0xec29c={'\u0078':[xF,xF],'\u0079':[xF,y_intersect],'\u0074\u0079\u0070\u0065':'scatter',"mode":'lines','\u006E\u0061\u006D\u0065':`q 线 (q=${q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](293880^293882)})`,'\u006C\u0069\u006E\u0065':{"color":'purple','\u0077\u0069\u0064\u0074\u0068':2,"dash":'dash'}};}else if(Math['\u0061\u0062\u0073'](q-(413866^413866))<_0xf11d){_0xec29c={'\u0078':[x_intersect,xF],'\u0079':[xF,xF],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":"\u006C\u0069\u006E\u0065\u0073","name":`q 线 (q=${q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](283957^283959)})`,'\u006C\u0069\u006E\u0065':{"color":'purple','\u0077\u0069\u0064\u0074\u0068':2,"dash":'dash'}};}else{_0xec29c={'\u0078':[xF,x_intersect],'\u0079':[xF,y_intersect],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':'lines',"name":`q 线 (q=${q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](706312^706314)})`,"line":{"color":'purple',"width":2,"dash":"\u0064\u0061\u0073\u0068"}};}_0x4fe['\u0070\u0075\u0073\u0068'](_0xec29c);var _0x6196b;const _0xd576ea=[{'\u0078':[xD,xD],'\u0079':[181909^181909,xD],"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':'lines','\u006E\u0061\u006D\u0065':'','\u006C\u0069\u006E\u0065':{"color":'red','\u0077\u0069\u0064\u0074\u0068':1,'\u0064\u0061\u0073\u0068':'dot'},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':false,"hoverinfo":"\u006E\u006F\u006E\u0065"},{'\u0078':[xW,xW],'\u0079':[951885^951885,xW],'\u0074\u0079\u0070\u0065':'scatter',"mode":"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':'','\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':'green','\u0077\u0069\u0064\u0074\u0068':1,"dash":'dot'},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':false,'\u0068\u006F\u0076\u0065\u0072\u0069\u006E\u0066\u006F':"\u006E\u006F\u006E\u0065"},{'\u0078':[xF,xF],'\u0079':[344488^344488,xF],"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":'lines',"name":'','\u006C\u0069\u006E\u0065':{"color":'purple','\u0077\u0069\u0064\u0074\u0068':1,'\u0064\u0061\u0073\u0068':"\u0064\u006F\u0074"},"showlegend":false,"hoverinfo":'none'}];_0x6196b=(328497^328503)+(937439^937430);_0x4fe['\u0070\u0075\u0073\u0068'](..._0xd576ea);var _0x068a0b;const _0x7a647f=[{'\u0078':xD,'\u0079':0,"text":"\u0078\u0044","showarrow":false,'\u0066\u006F\u006E\u0074':{'\u0073\u0069\u007A\u0065':14,"color":"\u0072\u0065\u0064","family":"\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066","weight":'bold'},'\u0079\u0073\u0068\u0069\u0066\u0074':-(918258^918246),'\u0078\u0061\u006E\u0063\u0068\u006F\u0072':"\u0063\u0065\u006E\u0074\u0065\u0072"},{'\u0078':xW,'\u0079':0,"text":'xW','\u0073\u0068\u006F\u0077\u0061\u0072\u0072\u006F\u0077':false,'\u0066\u006F\u006E\u0074':{"size":14,'\u0063\u006F\u006C\u006F\u0072':"\u0067\u0072\u0065\u0065\u006E","family":'Arial, sans-serif','\u0077\u0065\u0069\u0067\u0068\u0074':'bold'},'\u0079\u0073\u0068\u0069\u0066\u0074':-(865658^865646),'\u0078\u0061\u006E\u0063\u0068\u006F\u0072':"\u0063\u0065\u006E\u0074\u0065\u0072"},{'\u0078':xF,'\u0079':0,"text":"\u0078\u0046","showarrow":false,"font":{'\u0073\u0069\u007A\u0065':14,"color":"\u0070\u0075\u0072\u0070\u006C\u0065","family":'Arial, sans-serif','\u0077\u0065\u0069\u0067\u0068\u0074':"\u0062\u006F\u006C\u0064"},'\u0079\u0073\u0068\u0069\u0066\u0074':-(589870^589882),"xanchor":"\u0063\u0065\u006E\u0074\u0065\u0072"}];_0x068a0b=970603^970607;const _0xca4g={"title":"\u004D\u0063\u0043\u0061\u0062\u0065\u002D\u0054\u0068\u0069\u0065\u006C\u0065\u0020\u7CBE\u998F\u8BA1\u7B97\u56FE",'\u0078\u0061\u0078\u0069\u0073':{'\u0074\u0069\u0074\u006C\u0065':'液相乙醇摩尔分率 (x)','\u0072\u0061\u006E\u0067\u0065':[552000^552000,483962^483963]},"yaxis":{"title":'气相乙醇摩尔分率 (y)',"range":[579281^579281,372642^372643]},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':!![],'\u006C\u0065\u0067\u0065\u006E\u0064':{'\u0078':0.7,'\u0079':0.02,"bgcolor":"\u0072\u0067\u0062\u0061\u0028\u0032\u0035\u0035\u002C\u0020\u0032\u0035\u0035\u002C\u0020\u0032\u0035\u0035\u002C\u0020\u0030\u002E\u0038\u0029"},'\u0061\u006E\u006E\u006F\u0074\u0061\u0074\u0069\u006F\u006E\u0073':_0x7a647f,'\u0066\u006F\u006E\u0074':{"family":"\u004E\u006F\u0074\u006F\u0020\u0053\u0061\u006E\u0073\u0020\u0053\u0043\u002C\u0020\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066",'\u0073\u0069\u007A\u0065':14},"width":600,'\u0068\u0065\u0069\u0067\u0068\u0074':700,'\u006D\u0061\u0072\u0067\u0069\u006E':{'\u006C':60,'\u0072':30,'\u0062':60,'\u0074':80,"pad":4},"dragmode":false,'\u0068\u006F\u0076\u0065\u0072\u006D\u006F\u0064\u0065':false};Plotly['\u006E\u0065\u0077\u0050\u006C\u006F\u0074'](_0xdc98e,_0x4fe,_0xca4g);}function plotMcCabeThieleWithSteps(xD,xW,xF,q,R,results,rminData,_0xfac5fb,_0x451aa){const _0xac16a=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("tolp-ebaccm".split("").reverse().join(""));const _0xf4a0a=Array['\u0066\u0072\u006F\u006D']({"length":101},(_,i)=>i/(348017^347925));var _0xe5f6e=(378465^378467)+(855431^855429);const _0xa587e=_0xf4a0a['\u006D\u0061\u0070'](x=>equilibriumY(x));_0xe5f6e='\u0064\u006E\u0064\u0067\u0070\u006E';var _0xb9g8c;const _0x4dcefa=results['\u0078\u005F\u0069\u006E\u0074\u0065\u0072\u0073\u0065\u0063\u0074'];_0xb9g8c="nhcjfg".split("").reverse().join("");var _0x7f_0x73a=(421007^421000)+(369942^369943);const _0x84aa=results['\u0079\u005F\u0069\u006E\u0074\u0065\u0072\u0073\u0065\u0063\u0074'];_0x7f_0x73a="gcejcm".split("").reverse().join("");var _0x87e7e;const _0xdge26e=[{'\u0078':[173746^173746,129087^129086],'\u0079':[547899^547899,154873^154872],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':'lines',"name":'y = x',"line":{'\u0063\u006F\u006C\u006F\u0072':"\u0062\u006C\u0061\u0063\u006B","width":1}},{'\u0078':_0xf4a0a,'\u0079':_0xa587e,"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':'平衡线','\u006C\u0069\u006E\u0065':{"color":'blue',"width":2}},{'\u0078':x_ethanol_data,'\u0079':y_ethanol_data,"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073",'\u006E\u0061\u006D\u0065':"\u0056\u004C\u0045\u0020\u6570\u636E\u70B9",'\u006D\u0061\u0072\u006B\u0065\u0072':{"color":'blue','\u0073\u0069\u007A\u0065':6,'\u006F\u0070\u0061\u0063\u0069\u0074\u0079':0.7}},{'\u0078':[_0x4dcefa,xD],'\u0079':[_0x84aa,xD],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':'lines',"name":`精馏段操作线 (R=${R['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](119955^119953)})`,"line":{'\u0063\u006F\u006C\u006F\u0072':'red',"width":2}},{'\u0078':[xW,_0x4dcefa],'\u0079':[xW,_0x84aa],"type":'scatter','\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073","name":'提馏段操作线',"line":{'\u0063\u006F\u006C\u006F\u0072':"\u0067\u0072\u0065\u0065\u006E","width":2}},{'\u0078':[xD,xW,xF,_0x4dcefa],'\u0079':[xD,xW,xF,_0x84aa],"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073",'\u006E\u0061\u006D\u0065':"\u5173\u952E\u70B9\u0020\u0078\u0044","marker":{'\u0063\u006F\u006C\u006F\u0072':["\u0072\u0065\u0064","neerg".split("").reverse().join(""),"elprup".split("").reverse().join(""),"\u0062\u006C\u0061\u0063\u006B"],'\u0073\u0069\u007A\u0065':10,'\u0073\u0079\u006D\u0062\u006F\u006C':["\u0063\u0069\u0072\u0063\u006C\u0065","\u0063\u0069\u0072\u0063\u006C\u0065","elcric".split("").reverse().join(""),"nepo-elcric".split("").reverse().join("")]},"hoverinfo":"\u006E\u006F\u006E\u0065"}];_0x87e7e=(308066^308075)+(795412^795413);var _0xdb28d=(707833^707839)+(616054^616050);_0xdb28d=(696289^696289)+(115115^115114);_0x451aa=1e-9;if(Math['\u0061\u0062\u0073'](q-(827058^827059))<_0x451aa){_0xfac5fb={'\u0078':[xF,xF],'\u0079':[xF,_0x84aa],"type":'scatter','\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':`q 线 (q=${q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](412170^412168)})`,"line":{'\u0063\u006F\u006C\u006F\u0072':"\u0070\u0075\u0072\u0070\u006C\u0065",'\u0077\u0069\u0064\u0074\u0068':2,'\u0064\u0061\u0073\u0068':"\u0064\u0061\u0073\u0068"}};}else if(Math['\u0061\u0062\u0073'](q-(972943^972943))<_0x451aa){_0xfac5fb={'\u0078':[_0x4dcefa,xF],'\u0079':[xF,xF],'\u0074\u0079\u0070\u0065':'scatter',"mode":"\u006C\u0069\u006E\u0065\u0073","name":`q 线 (q=${q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](213223^213221)})`,'\u006C\u0069\u006E\u0065':{"color":"\u0070\u0075\u0072\u0070\u006C\u0065",'\u0077\u0069\u0064\u0074\u0068':2,'\u0064\u0061\u0073\u0068':"\u0064\u0061\u0073\u0068"}};}else{_0xfac5fb={'\u0078':[xF,_0x4dcefa],'\u0079':[xF,_0x84aa],"type":'scatter',"mode":'lines','\u006E\u0061\u006D\u0065':`q 线 (q=${q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](574805^574807)})`,'\u006C\u0069\u006E\u0065':{"color":'purple','\u0077\u0069\u0064\u0074\u0068':2,"dash":"\u0064\u0061\u0073\u0068"}};}_0xdge26e['\u0070\u0075\u0073\u0068'](_0xfac5fb);if(rminData&&!isNaN(rminData['\u0070\u0069\u006E\u0063\u0068\u0058'])&&!isNaN(rminData['\u0070\u0069\u006E\u0063\u0068\u0059'])){var _0xc_0xc8a;const _0xadfb={'\u0078':[rminData['\u0070\u0069\u006E\u0063\u0068\u0058']],'\u0079':[rminData['\u0070\u0069\u006E\u0063\u0068\u0059']],'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':'markers',"name":`夹紧点 (${rminData['\u0070\u0069\u006E\u0063\u0068\u0054\u0079\u0070\u0065']})`,"marker":{"color":'magenta',"size":12,"symbol":"\u0078"}};_0xc_0xc8a=(462195^462203)+(678533^678531);var _0x2c23b=(309755^309759)+(375008^375017);let _0xc6d81b;_0x2c23b=(638233^638232)+(103009^103009);if(isFinite(rminData['\u0052\u006D\u0069\u006E'])){_0xc6d81b=`Rmin 线 (${rminData['\u0052\u006D\u0069\u006E']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](168272^168275)})`;}else{_0xc6d81b="\u0052\u006D\u0069\u006E\u0020\u003D\u0020\u221E\u0020\u7EBF";}var _0x2c78eb;const _0x57e35e={'\u0078':[rminData['\u0070\u0069\u006E\u0063\u0068\u0058'],xD],'\u0079':[rminData['\u0070\u0069\u006E\u0063\u0068\u0059'],xD],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073","name":_0xc6d81b,"line":{'\u0063\u006F\u006C\u006F\u0072':"\u006D\u0061\u0067\u0065\u006E\u0074\u0061",'\u0077\u0069\u0064\u0074\u0068':1.5,"dash":"\u0064\u006F\u0074"}};_0x2c78eb=687537^687544;_0xdge26e['\u0070\u0075\u0073\u0068'](_0xadfb);_0xdge26e['\u0070\u0075\u0073\u0068'](_0x57e35e);}if(results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078']&&results['\u0073\u0074\u0065\u0070\u0073\u005F\u0079']&&results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078']['\u006C\u0065\u006E\u0067\u0074\u0068']>(691432^691433)){var _0x8444fb;const _0xe5e7ca={'\u0078':results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078'],'\u0079':results['\u0073\u0074\u0065\u0070\u0073\u005F\u0079'],'\u0074\u0079\u0070\u0065':"\u0073\u0063\u0061\u0074\u0074\u0065\u0072",'\u006D\u006F\u0064\u0065':"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':`阶梯线 (总板数: ${results['\u004E\u005F\u0074\u006F\u0074\u0061\u006C']})`,"line":{"color":"\u0064\u0061\u0072\u006B\u006F\u0072\u0061\u006E\u0067\u0065",'\u0077\u0069\u0064\u0074\u0068':2}};_0x8444fb=(323402^323401)+(488049^488051);const _0x2agdea={'\u0078':results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078'],'\u0079':results['\u0073\u0074\u0065\u0070\u0073\u005F\u0079'],'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073","name":"\u9636\u68AF\u70B9",'\u006D\u0061\u0072\u006B\u0065\u0072':{'\u0063\u006F\u006C\u006F\u0072':'darkorange','\u0073\u0069\u007A\u0065':6},"showlegend":false};_0xdge26e['\u0070\u0075\u0073\u0068'](_0xe5e7ca);_0xdge26e['\u0070\u0075\u0073\u0068'](_0x2agdea);if(results['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']>(145675^145675)&&results['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']<=results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078']['\u006C\u0065\u006E\u0067\u0074\u0068']/(166765^166767)){const _0x57e76a=results['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']*(756552^756554)-(447665^447664);if(_0x57e76a<results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078']['\u006C\u0065\u006E\u0067\u0074\u0068']){var _0x9d95a=(198942^198937)+(110114^110115);const _0x68b8ea={'\u0078':[results['\u0073\u0074\u0065\u0070\u0073\u005F\u0078'][_0x57e76a]],'\u0079':[results['\u0073\u0074\u0065\u0070\u0073\u005F\u0079'][_0x57e76a]],'\u0074\u0079\u0070\u0065':'scatter','\u006D\u006F\u0064\u0065':"\u006D\u0061\u0072\u006B\u0065\u0072\u0073","name":`进料板 (第 ${results['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']} 板)`,'\u006D\u0061\u0072\u006B\u0065\u0072':{"color":'purple',"size":12,'\u0073\u0079\u006D\u0062\u006F\u006C':'star'}};_0x9d95a=460376^460369;_0xdge26e['\u0070\u0075\u0073\u0068'](_0x68b8ea);}}}var _0xed6e3d=(297707^297706)+(581104^581104);const _0xc0a79a=[{'\u0078':[xD,xD],'\u0079':[910917^910917,xD],'\u0074\u0079\u0070\u0065':'scatter',"mode":"\u006C\u0069\u006E\u0065\u0073","name":'','\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':'red','\u0077\u0069\u0064\u0074\u0068':1,'\u0064\u0061\u0073\u0068':'dot'},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':false,'\u0068\u006F\u0076\u0065\u0072\u0069\u006E\u0066\u006F':'none'},{'\u0078':[xW,xW],'\u0079':[903360^903360,xW],"type":"\u0073\u0063\u0061\u0074\u0074\u0065\u0072","mode":"\u006C\u0069\u006E\u0065\u0073","name":'','\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':'green','\u0077\u0069\u0064\u0074\u0068':1,'\u0064\u0061\u0073\u0068':"\u0064\u006F\u0074"},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':false,"hoverinfo":'none'},{'\u0078':[xF,xF],'\u0079':[323993^323993,xF],'\u0074\u0079\u0070\u0065':'scatter',"mode":"\u006C\u0069\u006E\u0065\u0073",'\u006E\u0061\u006D\u0065':'','\u006C\u0069\u006E\u0065':{'\u0063\u006F\u006C\u006F\u0072':"\u0070\u0075\u0072\u0070\u006C\u0065",'\u0077\u0069\u0064\u0074\u0068':1,"dash":"\u0064\u006F\u0074"},'\u0073\u0068\u006F\u0077\u006C\u0065\u0067\u0065\u006E\u0064':false,'\u0068\u006F\u0076\u0065\u0072\u0069\u006E\u0066\u006F':"\u006E\u006F\u006E\u0065"}];_0xed6e3d=568360^568360;_0xdge26e['\u0070\u0075\u0073\u0068'](..._0xc0a79a);var _0xc2546d=(485776^485777)+(612166^612160);const _0x94de=[{'\u0078':xD,'\u0079':0,'\u0074\u0065\u0078\u0074':"\u0078\u0044",'\u0073\u0068\u006F\u0077\u0061\u0072\u0072\u006F\u0077':false,"font":{"size":14,'\u0063\u006F\u006C\u006F\u0072':"\u0072\u0065\u0064","family":"\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066",'\u0077\u0065\u0069\u0067\u0068\u0074':'bold'},'\u0079\u0073\u0068\u0069\u0066\u0074':-(932836^932848),"xanchor":"\u0063\u0065\u006E\u0074\u0065\u0072"},{'\u0078':xW,'\u0079':0,"text":"\u0078\u0057","showarrow":false,"font":{"size":14,"color":'green',"family":'Arial, sans-serif','\u0077\u0065\u0069\u0067\u0068\u0074':'bold'},'\u0079\u0073\u0068\u0069\u0066\u0074':-(654382^654394),'\u0078\u0061\u006E\u0063\u0068\u006F\u0072':'center'},{'\u0078':xF,'\u0079':0,'\u0074\u0065\u0078\u0074':"\u0078\u0046",'\u0073\u0068\u006F\u0077\u0061\u0072\u0072\u006F\u0077':false,"font":{"size":14,'\u0063\u006F\u006C\u006F\u0072':'purple',"family":'Arial, sans-serif',"weight":"\u0062\u006F\u006C\u0064"},"yshift":-(331251^331239),'\u0078\u0061\u006E\u0063\u0068\u006F\u0072':"\u0063\u0065\u006E\u0074\u0065\u0072"}];_0xc2546d=515975^515974;const _0x8ge2b={"title":"\u004D\u0063\u0043\u0061\u0062\u0065\u002D\u0054\u0068\u0069\u0065\u006C\u0065\u0020\u7CBE\u998F\u8BA1\u7B97\u56FE",'\u0078\u0061\u0078\u0069\u0073':{"title":'液相乙醇摩尔分率 (x)','\u0072\u0061\u006E\u0067\u0065':[791439^791439,827760^827761]},"yaxis":{"title":'气相乙醇摩尔分率 (y)',"range":[106935^106935,553229^553228]},"showlegend":!![],'\u006C\u0065\u0067\u0065\u006E\u0064':{'\u0078':0.7,'\u0079':0.02,'\u0062\u0067\u0063\u006F\u006C\u006F\u0072':"\u0072\u0067\u0062\u0061\u0028\u0032\u0035\u0035\u002C\u0020\u0032\u0035\u0035\u002C\u0020\u0032\u0035\u0035\u002C\u0020\u0030\u002E\u0038\u0029"},'\u0061\u006E\u006E\u006F\u0074\u0061\u0074\u0069\u006F\u006E\u0073':_0x94de,'\u0066\u006F\u006E\u0074':{'\u0066\u0061\u006D\u0069\u006C\u0079':"\u004E\u006F\u0074\u006F\u0020\u0053\u0061\u006E\u0073\u0020\u0053\u0043\u002C\u0020\u0041\u0072\u0069\u0061\u006C\u002C\u0020\u0073\u0061\u006E\u0073\u002D\u0073\u0065\u0072\u0069\u0066",'\u0073\u0069\u007A\u0065':14},'\u0077\u0069\u0064\u0074\u0068':600,'\u0068\u0065\u0069\u0067\u0068\u0074':700,'\u006D\u0061\u0072\u0067\u0069\u006E':{'\u006C':60,'\u0072':30,'\u0062':60,'\u0074':80,'\u0070\u0061\u0064':4},"dragmode":false,'\u0068\u006F\u0076\u0065\u0072\u006D\u006F\u0064\u0065':false};Plotly['\u006E\u0065\u0077\u0050\u006C\u006F\u0074'](_0xac16a,_0xdge26e,_0x8ge2b);}function findQLineEquilibriumIntersection(q,xF,_0x4c_0xf1d,_0x6343ba,_0x4791b){var _0xc2_0x8g1=(868480^868487)+(294075^294075);_0x4c_0xf1d=1e-9;_0xc2_0x8g1=975945^975937;if(Math['\u0061\u0062\u0073'](q-(432471^432470))<_0x4c_0xf1d){return[xF,equilibriumY(xF)];}_0x6343ba=0.001;var _0xcc7bcf;_0x4791b=0.3;_0xcc7bcf=(307436^307438)+(931702^931701);let _0x56e3bc=Infinity;let _0xa2899g=NaN;for(let x=Math['\u006D\u0061\u0078'](907963^907963,xF-_0x4791b/(517246^517244));x<=Math['\u006D\u0069\u006E'](896688^896689,xF+_0x4791b/(595357^595359));x+=_0x6343ba){var _0xe45fae=(363490^363495)+(391255^391263);const _0xe6c=equilibriumY(x);_0xe45fae=(418587^418578)+(439591^439586);const _0x4g647c=qLineY(x,q,xF);if(!isNaN(_0x4g647c)){var _0x62c71d;const _0xdf9e=Math['\u0061\u0062\u0073'](_0xe6c-_0x4g647c);_0x62c71d=(905846^905855)+(554831^554822);if(_0xdf9e<_0x56e3bc){_0x56e3bc=_0xdf9e;_0xa2899g=x;}}}if(_0x56e3bc<0.01){return[_0xa2899g,equilibriumY(_0xa2899g)];}else{console['\u0077\u0061\u0072\u006E']("\u672A\u80FD\u627E\u5230\u0020\u0071\u0020\u7EBF\u548C\u5E73\u8861\u7EBF\u7684\u7CBE\u786E\u4EA4\u70B9");return[NaN,NaN];}}function calculateRminFromPoint(xD,xp,yp,_0xdebda){_0xdebda=1e-9;if(yp<xp-_0xdebda)return NaN;if(Math['\u0061\u0062\u0073'](xp-xD)<_0xdebda&&Math['\u0061\u0062\u0073'](yp-xD)<_0xdebda)return Infinity;if(Math['\u0061\u0062\u0073'](yp-xp)<_0xdebda)return Infinity;var _0x1g2c5a=(468884^468885)+(550564^550566);const _0xd639gf=(xD-yp)/(yp-xp);_0x1g2c5a=(430914^430913)+(178894^178887);return Math['\u006D\u0061\u0078'](112220^112220,_0xd639gf);}function findTangentBasedRmin(xD,xStartSearch,xEndSearch=null,_0xc29f1f,_0x3b90db,_0xd2_0x1da){_0xc29f1f=1e-9;if(xEndSearch===null){xEndSearch=xD;}if(xStartSearch>=xEndSearch-_0xc29f1f){console['\u0077\u0061\u0072\u006E'](`警告: 搜索范围无效 [${xStartSearch['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](958243^958247)}, ${xEndSearch['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](141818^141822)}]`);return[NaN,NaN,NaN];}if(xStartSearch>=xD-_0xc29f1f){console['\u0077\u0061\u0072\u006E'](`警告: 起始搜索点 ${xStartSearch['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](451086^451082)} 不小于 xD ${xD['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](904576^904580)}`);return[NaN,NaN,NaN];}const _0xagbff=Math['\u006D\u0061\u0078'](289412^289412,xStartSearch+_0xc29f1f);var _0x5f4f8c=(660433^660440)+(158552^158556);const _0x244e=Math['\u006D\u0069\u006E'](xEndSearch,xD-_0xc29f1f);_0x5f4f8c=(170112^170117)+(852962^852971);if(_0xagbff>=_0x244e){console['\u0077\u0061\u0072\u006E'](`警告: 调整后的搜索范围无效 [${_0xagbff['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](331637^331633)}, ${_0x244e['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](205574^205570)}]`);return[NaN,NaN,NaN];}_0x3b90db=278577^278981;const _0x75cc=[];var _0x3g_0x139=(217918^217915)+(896594^896592);const _0xf_0xbe7=(_0x244e-_0xagbff)/(_0x3b90db-(952049^952048));_0x3g_0x139=863981^863980;for(let i=493495^493495;i<_0x3b90db;i++){_0x75cc['\u0070\u0075\u0073\u0068'](_0xagbff+i*_0xf_0xbe7);}const _0x4d2bf=_0x75cc['\u006D\u0061\u0070'](x=>equilibriumY(x));var _0xc4ccf=(757161^757162)+(760253^760251);let _0xafc60d=[];_0xc4ccf=(809263^809261)+(639179^639177);let _0xdec76b=[];let _0x6efe=[];for(let i=151696^151696;i<_0x75cc['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){const _0xdg_0xgc0=_0x75cc[i];var _0x129g8e=(528712^528719)+(823299^823296);const _0x2c_0x0d2=_0x4d2bf[i];_0x129g8e=161558^161553;if(_0x2c_0x0d2>_0xdg_0xgc0+_0xc29f1f&&_0xdg_0xgc0<xD-_0xc29f1f){const _0xaef42d=calculateRminFromPoint(xD,_0xdg_0xgc0,_0x2c_0x0d2);if(!isNaN(_0xaef42d)&&isFinite(_0xaef42d)&&_0xaef42d>=(322298^322298)){_0xafc60d['\u0070\u0075\u0073\u0068'](_0xaef42d);_0xdec76b['\u0070\u0075\u0073\u0068'](_0xdg_0xgc0);_0x6efe['\u0070\u0075\u0073\u0068'](_0x2c_0x0d2);}}}if(_0xafc60d['\u006C\u0065\u006E\u0067\u0074\u0068']===(697825^697825)){console['\u0077\u0061\u0072\u006E'](`警告: 在范围内未找到有效的平衡点计算切线 Rmin`);return[NaN,NaN,NaN];}var _0x7ff;_0xd2_0x1da=812325^812325;_0x7ff=(704214^704222)+(634235^634235);var _0xe7gb=(158053^158060)+(383508^383510);let _0xb12e1f=_0xafc60d[248287^248287];_0xe7gb=(998219^998221)+(412605^412602);for(let i=711727^711726;i<_0xafc60d['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){if(_0xafc60d[i]>_0xb12e1f){_0xb12e1f=_0xafc60d[i];_0xd2_0x1da=i;}}return[_0xafc60d[_0xd2_0x1da],_0xdec76b[_0xd2_0x1da],_0x6efe[_0xd2_0x1da]];}function calculateRmin(xD,xF,tF,_0x2feg2a){console['\u006C\u006F\u0067'](`\n计算 Rmin: xD=${xD['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](873989^873985)}, xF=${xF['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](807546^807550)}, tF=${tF['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](787237^787236)}°C`);_0x2feg2a=1e-7;let _0xfb2={'\u0052\u006D\u0069\u006E':NaN,"pinchX":NaN,"pinchY":NaN,'\u0070\u0069\u006E\u0063\u0068\u0054\u0079\u0070\u0065':"\u672A\u77E5",'\u0071':NaN};try{var _0x60d;const q=calculateQValue(xF,tF);_0x60d=855193^855196;if(isNaN(q))throw new Error("\u0071\u0020\u8BA1\u7B97\u9519\u8BEF");_0xfb2['\u0071']=q;const[xp_q,yp_q]=findQLineEquilibriumIntersection(q,xF);const _0xe68ad=!isNaN(xp_q)&&!isNaN(yp_q);let _0x4ff87d=NaN,_0x6c3db=NaN,_0x2ddbba=NaN,_0x61daa=NaN;if(_0xe68ad){_0x4ff87d=calculateRminFromPoint(xD,xp_q,yp_q);console['\u006C\u006F\u0067'](`  -> q 线交点 Pq: (${xp_q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](953264^953268)}, ${yp_q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](191021^191017)})`);if(!isNaN(_0x4ff87d)){console['\u006C\u006F\u0067'](`  -> 基于 Pq 的 Rmin_q: ${isFinite(_0x4ff87d)?_0x4ff87d['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](756210^756214):"\u221E"}`);}else{console['\u006C\u006F\u0067']("q_nimR \u7B97\u8BA1 qP \u4ECE\u6CD5\u65E0 >-  ".split("").reverse().join(""));}}else{console['\u006C\u006F\u0067']("\u0020\u0020\u002D\u003E\u0020\u672A\u80FD\u627E\u5230\u6709\u6548\u7684\u0020\u0071\u0020\u7EBF\u002F\u5E73\u8861\u7EBF\u4EA4\u70B9\u3002\u5C1D\u8BD5\u76F4\u63A5\u5BFB\u627E\u5207\u70B9\u3002");}var _0xba933f=(362854^362851)+(610123^610125);let _0xe1fe=false;_0xba933f=(442938^442942)+(588180^588180);if(_0xe68ad&&!isNaN(_0x4ff87d)){const[Rmin_t_candidate,xt_t_candidate,yt_t_candidate]=findTangentBasedRmin(xD,xp_q,xD);if(!isNaN(Rmin_t_candidate)&&isFinite(Rmin_t_candidate)&&Rmin_t_candidate>=(492234^492234)){console['\u006C\u006F\u0067'](`  -> 在 Pq 右侧找到潜在切点 Pt': (${xt_t_candidate['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](541061^541057)}, ${yt_t_candidate['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](614689^614693)})`);console['\u006C\u006F\u0067'](`  -> 基于 Pt' 的 Rmin_t': ${Rmin_t_candidate['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](864022^864018)}`);if(Rmin_t_candidate>_0x4ff87d+_0x2feg2a){_0xe1fe=!![];_0x6c3db=Rmin_t_candidate;_0x2ddbba=xt_t_candidate;_0x61daa=yt_t_candidate;console['\u006C\u006F\u0067'](`  -> 条件满足: Rmin_t' (${_0x6c3db['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](251648^251652)}) > Rmin_q (${_0x4ff87d['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](718218^718222)})。需要寻找最终切点。`);}else{console['\u006C\u006F\u0067'](`  -> 条件不满足: Rmin_t' (${Rmin_t_candidate['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](468927^468923)}) <= Rmin_q (${_0x4ff87d['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](856183^856179)})。维持 Pq 为控制点。`);}}else{console['\u006C\u006F\u0067'](`  -> 在 Pq 右侧未找到需要有限 Rmin 的有效切点。`);var _0x1f540d=(635974^635975)+(437156^437156);const _0xab51af=!isNaN(Rmin_t_candidate)&&!isFinite(Rmin_t_candidate);_0x1f540d=(168531^168534)+(841384^841386);if(_0xab51af&&isFinite(_0x4ff87d)){_0xe1fe=!![];_0x6c3db=Rmin_t_candidate;_0x2ddbba=xt_t_candidate;_0x61daa=yt_t_candidate;console['\u006C\u006F\u0067'](`  -> 条件满足: Pq 右侧切点需要无限 Rmin，而 Rmin_q 有限。`);}}}if(_0xe1fe){if(!isNaN(_0x6c3db)){_0xfb2['\u0052\u006D\u0069\u006E']=_0x6c3db;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0058']=_0x2ddbba;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0059']=_0x61daa;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0054\u0079\u0070\u0065']="\u5207\u70B9";console['\u006C\u006F\u0067'](`  -> 最终确定: Rmin 由切点 Pt 控制。`);}else{throw new Error("\u903B\u8F91\u9519\u8BEF\uFF1A\u9700\u8981\u5207\u70B9\u4F46\u672A\u8BB0\u5F55\u6709\u6548\u7684\u5207\u70B9\u0020\u0052\u006D\u0069\u006E\u3002");}}else if(_0xe68ad&&!isNaN(_0x4ff87d)){_0xfb2['\u0052\u006D\u0069\u006E']=_0x4ff87d;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0058']=xp_q;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0059']=yp_q;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0054\u0079\u0070\u0065']="\u0071\u002D\u7EBF\u4EA4\u70B9";console['\u006C\u006F\u0067'](`  -> 最终确定: Rmin 由 q 线交点 Pq 控制。`);}else{console['\u006C\u006F\u0067']("\u0020\u0020\u002D\u003E\u0020\u56DE\u9000\uFF1A\u0050\u0071\u0020\u6216\u0020\u0052\u006D\u0069\u006E\u005F\u0071\u0020\u65E0\u6548\uFF0C\u5C1D\u8BD5\u67E5\u627E\u5168\u5C40\u5207\u70B9\u0020\u0028\u0078\u0046\u0020\u5230\u0020\u0078\u0044\u0029\u3002");const[Rmin_t_global,xt_t_global,yt_t_global]=findTangentBasedRmin(xD,xF,xD);if(!isNaN(Rmin_t_global)){_0xfb2['\u0052\u006D\u0069\u006E']=Rmin_t_global;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0058']=xt_t_global;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0059']=yt_t_global;_0xfb2['\u0070\u0069\u006E\u0063\u0068\u0054\u0079\u0070\u0065']=")\u9000\u56DE\u5C40\u5168( \u70B9\u5207".split("").reverse().join("");console['\u006C\u006F\u0067'](`  -> 全局切点 Pt_g: (${xt_t_global['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](922390^922386)}, ${yt_t_global['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](660199^660195)}), Rmin_g: ${isFinite(_0xfb2['\u0052\u006D\u0069\u006E'])?_0xfb2['\u0052\u006D\u0069\u006E']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](404564^404560):"\u221E"}`);}else{throw new Error("\u65E0\u6CD5\u786E\u5B9A\u6709\u6548\u7684\u6700\u5C0F\u56DE\u6D41\u6BD4\u0020\u0028\u0050\u0071\u0020\u65E0\u6548\u4E14\u672A\u627E\u5230\u5168\u5C40\u5207\u70B9\u0029\u3002");}}if(isNaN(_0xfb2['\u0052\u006D\u0069\u006E'])){throw new Error("\u6700\u7EC8\u0020\u0052\u006D\u0069\u006E\u0020\u8BA1\u7B97\u7ED3\u679C\u4E3A\u0020\u004E\u0061\u004E\u3002");}return _0xfb2;}catch(e){console['\u0065\u0072\u0072\u006F\u0072'](`错误 (calculateRmin): ${e['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']}`);_0xfb2['\u0065\u0072\u0072\u006F\u0072']=e['\u006D\u0065\u0073\u0073\u0061\u0067\u0065'];return _0xfb2;}}function calculateNmin(xD,xW,_0xbf2e,_0xbea,_0x77ccfe){var _0xb4ba;_0xbf2e=396984^396984;_0xb4ba='\u0063\u0067\u0064\u0066\u006C\u0066';let _0x9368af=parseFloat(xD);const _0xd28d=parseFloat(xW);var _0x57ba5d;_0xbea=558137^558173;_0x57ba5d="ldagkl".split("").reverse().join("");_0x77ccfe=1e-7;if(!((283026^283026)<=xD&&xD<=(188199^188198)&&(343610^343610)<=_0xd28d&&_0xd28d<=(604347^604346)&&_0xd28d<xD)){return-(522302^522303);}while(_0xbf2e<_0xbea){var _0x2_0x588;const _0xf299a=equilibriumX(_0x9368af);_0x2_0x588='\u006B\u0064\u0070\u006F\u006B\u006E';if(isNaN(_0xf299a)){return-(714281^714280);}if(_0xf299a<=_0xd28d+_0x77ccfe){_0xbf2e+=895231^895230;break;}_0x9368af=_0xf299a;_0xbf2e+=844496^844497;}if(_0xbf2e>=_0xbea){console['\u0077\u0061\u0072\u006E'](`警告 (Nmin 计算): 达到最大阶段数 ${_0xbea} 未达到 xW。`);return-(255048^255049);}return _0xbf2e;}function performMcCabeThiele(xD,xW,xF,q,R,_0xbbcae){var _0xe5db;_0xbbcae=1e-7;_0xe5db=(270528^270529)+(595951^595943);var _0x5g_0x2b0=(667299^667297)+(233014^233009);const _0xadffae={'\u0073\u0074\u0065\u0070\u0073\u005F\u0078':[],'\u0073\u0074\u0065\u0070\u0073\u005F\u0079':[],'\u004E\u005F\u0074\u006F\u0074\u0061\u006C':0,'\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065':-(762791^762790),'\u0065\u0072\u0072\u006F\u0072':null,"x_intersect":NaN,"y_intersect":NaN,"stripping_slope":NaN,'\u0073\u0074\u0072\u0069\u0070\u0070\u0069\u006E\u0067\u005F\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074':NaN};_0x5g_0x2b0=799238^799233;try{if(!((273033^273033)<=xD&&xD<=(309638^309639)))throw new Error("\u5854\u9876\u7EC4\u6210\u0020\u0078\u0044\u0020\u5FC5\u987B\u5728\u0020\u005B\u0030\u002C\u0020\u0031\u005D\u0020\u8303\u56F4\u5185");if(!((563685^563685)<=xW&&xW<=(325363^325362)))throw new Error("\u5854\u5E95\u7EC4\u6210\u0020\u0078\u0057\u0020\u5FC5\u987B\u5728\u0020\u005B\u0030\u002C\u0020\u0031\u005D\u0020\u8303\u56F4\u5185");if(!((916112^916112)<=xF&&xF<=(237624^237625)))throw new Error("\u8FDB\u6599\u7EC4\u6210\u0020\u0078\u0046\u0020\u5FC5\u987B\u5728\u0020\u005B\u0030\u002C\u0020\u0031\u005D\u0020\u8303\u56F4\u5185");if(!(xW<xD))throw new Error("Wx \u6210\u7EC4\u5E95\u5854\u4E8E\u5927\u987B\u5FC5 Dx \u6210\u7EC4\u9876\u5854".split("").reverse().join(""));if(R<=(839826^839826))throw new Error("0 \u4E8E\u5927\u987B\u5FC5 R \u6BD4\u6D41\u56DE".split("").reverse().join(""));const[x_intersect,y_intersect]=findIntersection(R,xD,q,xF);_0xadffae['\u0078\u005F\u0069\u006E\u0074\u0065\u0072\u0073\u0065\u0063\u0074']=x_intersect;_0xadffae['\u0079\u005F\u0069\u006E\u0074\u0065\u0072\u0073\u0065\u0063\u0074']=y_intersect;if(isNaN(x_intersect)){throw new Error("\u65E0\u6CD5\u8BA1\u7B97\u64CD\u4F5C\u7EBF\u4EA4\u70B9\u3002\u8BF7\u68C0\u67E5\u0020\u0052\u0020\u548C\u0020\u0071\u0020\u503C\u3002");}if(y_intersect<x_intersect-1e-6){throw new Error(`操作线交点 (${x_intersect['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](123182^123178)}, ${y_intersect['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](120038^120034)}) 低于 y=x 线。无法操作 (可能 R < Rmin)。`);}const[strip_slope,strip_intercept]=strippingOpLineCoeffs(x_intersect,y_intersect,xW);_0xadffae['\u0073\u0074\u0072\u0069\u0070\u0070\u0069\u006E\u0067\u005F\u0073\u006C\u006F\u0070\u0065']=strip_slope;_0xadffae['\u0073\u0074\u0072\u0069\u0070\u0070\u0069\u006E\u0067\u005F\u0069\u006E\u0074\u0065\u0072\u0063\u0065\u0070\u0074']=strip_intercept;if(isNaN(strip_slope)){var _0xe61da;const _0xac13c=equilibriumY(xW);_0xe61da=439941^439939;if(!isNaN(_0xac13c)&&y_intersect<_0xac13c-1e-6){throw new Error(`无法达到目标 xW=${xW['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](420782^420778)}。操作线交点 y=${y_intersect['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](603549^603545)} 低于 xW 对应的平衡点 y=${_0xac13c['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](957773^957769)}。`);}else{throw new Error("\u65E0\u6CD5\u786E\u5B9A\u63D0\u998F\u6BB5\u64CD\u4F5C\u7EBF\uFF08\u4EA4\u70B9\u53EF\u80FD\u63A5\u8FD1\u0020\u0028\u0078\u0057\u002C\u0020\u0078\u0057\u0029\u0020\u6216\u63A5\u8FD1\u5782\u76F4\uFF09\u3002");}}let _0x832b=xD,_0xd9de=xD;var _0x9a_0xc59=(770174^770174)+(568800^568803);let _0xb21a9g=457528^457528;_0x9a_0xc59="gpifka".split("").reverse().join("");const _0xd48d3f=[xD],_0x8699db=[xD];var _0xacbd6a=(596575^596567)+(594907^594899);const _0x93dd8b=479106^479206;_0xacbd6a=957710^957705;let _0xbd26c=-(718015^718014);while(_0xb21a9g<_0x93dd8b){var _0xc5997e=(312732^312724)+(352076^352078);const _0xbgfcgb=_0xb21a9g+(149213^149212);_0xc5997e=(554528^554533)+(263855^263854);const _0x9c52b=equilibriumX(_0xd9de);if(isNaN(_0x9c52b)){throw new Error(`第 ${_0xbgfcgb} 板平衡液相计算失败 (y=${_0xd9de['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](197428^197424)})`);}var _0xfb_0x317=(684708^684710)+(890406^890400);const _0xe_0x28c=Math['\u006D\u0061\u0078'](384931^384931,_0x9c52b);_0xfb_0x317=(890088^890088)+(762560^762565);_0xd48d3f['\u0070\u0075\u0073\u0068'](_0xe_0x28c);_0x8699db['\u0070\u0075\u0073\u0068'](_0xd9de);if(_0xe_0x28c<=xW+_0xbbcae){_0xb21a9g+=270256^270257;break;}const _0x1fa4ae=_0xe_0x28c>=x_intersect-_0xbbcae;if(!_0x1fa4ae&&_0xbd26c===-(229649^229648)){_0xbd26c=_0xbgfcgb;}var _0x3_0x5ab=(750648^750640)+(646729^646721);let _0x49fd;_0x3_0x5ab='\u0062\u0062\u0062\u0067\u0069\u0066';if(_0x1fa4ae){_0x49fd=rectifyingOpLine(_0xe_0x28c,R,xD);if(isNaN(_0x49fd)){throw new Error(`第 ${_0xbgfcgb} 板精馏段操作线计算失败 (x=${_0xe_0x28c['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](702805^702801)})`);}}else{_0x49fd=strip_slope*_0xe_0x28c+strip_intercept;if(isNaN(_0x49fd)){throw new Error(`第 ${_0xbgfcgb} 板提馏段操作线计算失败 (x=${_0xe_0x28c['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](882572^882568)})`);}if(_0x49fd<_0xe_0x28c-_0xbbcae){_0x49fd=_0xe_0x28c;}}var _0x82dfbb=(562296^562298)+(198039^198038);const _0x6b_0x425=Math['\u006D\u0069\u006E'](813193^813192,_0x49fd);_0x82dfbb=621349^621356;_0xd48d3f['\u0070\u0075\u0073\u0068'](_0xe_0x28c);_0x8699db['\u0070\u0075\u0073\u0068'](_0x6b_0x425);_0x832b=_0xe_0x28c;_0xd9de=_0x6b_0x425;_0xb21a9g+=358833^358832;}_0xadffae['\u0073\u0074\u0065\u0070\u0073\u005F\u0078']=_0xd48d3f;_0xadffae['\u0073\u0074\u0065\u0070\u0073\u005F\u0079']=_0x8699db;_0xadffae['\u004E\u005F\u0074\u006F\u0074\u0061\u006C']=_0xb21a9g;if(_0xb21a9g===_0x93dd8b&&_0x832b>xW+_0xbbcae){_0xadffae['\u0065\u0072\u0072\u006F\u0072']=`达到最大迭代次数 (${_0x93dd8b}) 未达到 xW (停止于 x=${_0x832b['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](717526^717522)})`;}if(_0xbd26c!==-(809739^809738)){_0xadffae['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']=_0xbd26c;}else{_0xadffae['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']=_0xadffae['\u004E\u005F\u0074\u006F\u0074\u0061\u006C']>(712532^712532)?_0xadffae['\u004E\u005F\u0074\u006F\u0074\u0061\u006C']:440502^440502;}if(_0xadffae['\u004E\u005F\u0074\u006F\u0074\u0061\u006C']>(250885^250885)){_0xadffae['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']=Math['\u006D\u0061\u0078'](715953^715952,Math['\u006D\u0069\u006E'](_0xadffae['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065'],_0xadffae['\u004E\u005F\u0074\u006F\u0074\u0061\u006C']));}else{_0xadffae['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065']=673813^673813;}return _0xadffae;}catch(e){_0xadffae['\u0065\u0072\u0072\u006F\u0072']=e['\u006D\u0065\u0073\u0073\u0061\u0067\u0065'];console['\u0065\u0072\u0072\u006F\u0072']("\u004D\u0063\u0043\u0061\u0062\u0065\u002D\u0054\u0068\u0069\u0065\u006C\u0065\u0020\u0045\u0072\u0072\u006F\u0072\u003A",e['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']);return _0xadffae;}}document['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0044\u004F\u004D\u0043\u006F\u006E\u0074\u0065\u006E\u0074\u004C\u006F\u0061\u0064\u0065\u0064",function(){const _0xca_0x316=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0063\u0061\u006C\u0063\u0075\u006C\u0061\u0074\u0065\u002D\u0062\u0074\u006E");_0xca_0x316['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u006C\u0069\u0063\u006B",function(){const _0xdee63d=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("Dx".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065']);var _0x707e=(154537^154540)+(614130^614139);const _0x6_0xca5=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("Wx".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065']);_0x707e=(211950^211946)+(542973^542965);const _0x5bf=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("Fx".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065']);var _0x1c_0xad3=(806397^806393)+(795823^795814);const _0x1b_0x383=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("Ft".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065']);_0x1c_0xad3='\u0065\u006A\u0065\u0067\u006C\u0070';const R=parseFloat(document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0052")['\u0076\u0061\u006C\u0075\u0065']);if(isNaN(_0xdee63d)||isNaN(_0x6_0xca5)||isNaN(_0x5bf)||isNaN(_0x1b_0x383)||isNaN(R)){alert("\u8BF7\u8F93\u5165\u6240\u6709\u5FC5\u8981\u7684\u53C2\u6570");return;}if(_0xdee63d<=_0x6_0xca5){alert("\u5854\u9876\u7EC4\u6210\u0020\u0078\u0044\u0020\u5FC5\u987B\u5927\u4E8E\u5854\u5E95\u7EC4\u6210\u0020\u0078\u0057");return;}if(R<=(837912^837912)){alert("\u56DE\u6D41\u6BD4\u0020\u0052\u0020\u5FC5\u987B\u5927\u4E8E\u0020\u0030");return;}var _0xbgf59g=(276642^276650)+(903702^903696);const q=calculateQValue(_0x5bf,_0x1b_0x383);_0xbgf59g="jnfojb".split("").reverse().join("");if(isNaN(q)){alert("\u5EA6\u6E29\u548C\u6210\u7EC4\u6599\u8FDB\u67E5\u68C0\u8BF7\uFF0C\u8D25\u5931\u503C q \u7B97\u8BA1".split("").reverse().join(""));return;}document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0071\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=q['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](888764^888767);const _0xaaa6a=calculateRmin(_0xdee63d,_0x5bf,_0x1b_0x383);if(!isNaN(_0xaaa6a['\u0052\u006D\u0069\u006E'])){if(isFinite(_0xaaa6a['\u0052\u006D\u0069\u006E'])){document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-nimr".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0xaaa6a['\u0052\u006D\u0069\u006E']['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](265770^265769);}else{document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-nimr".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u221E";}}else{document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-nimr".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u8BA1\u7B97\u9519\u8BEF";}var _0xc40c=(175310^175307)+(495398^495407);const _0x7_0x17c=calculateNmin(_0xdee63d,_0x6_0xca5);_0xc40c="jliqha".split("").reverse().join("");if(_0x7_0x17c>=(300529^300529)){document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-nimn".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x7_0x17c['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']();}else{document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006E\u006D\u0069\u006E\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u8BA1\u7B97\u9519\u8BEF";}var _0x4_0xd8e;const _0xbbf3de=performMcCabeThiele(_0xdee63d,_0x6_0xca5,_0x5bf,q,R);_0x4_0xd8e="ondpnn".split("").reverse().join("");if(_0xbbf3de['\u0065\u0072\u0072\u006F\u0072']){alert("\u004D\u0063\u0043\u0061\u0062\u0065\u002D\u0054\u0068\u0069\u0065\u006C\u0065\u0020\u8BA1\u7B97\u9519\u8BEF\u003A\u0020"+_0xbbf3de['\u0065\u0072\u0072\u006F\u0072']);document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006E\u002D\u0074\u006F\u0074\u0061\u006C\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u8BEF\u9519".split("").reverse().join("");document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0066\u0065\u0065\u0064\u002D\u0073\u0074\u0061\u0067\u0065\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u8BEF\u9519".split("").reverse().join("");document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006E\u002D\u0072\u0065\u0063\u0074\u0069\u0066\u0079\u0069\u006E\u0067\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u9519\u8BEF";document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006E\u002D\u0073\u0074\u0072\u0069\u0070\u0070\u0069\u006E\u0067\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u9519\u8BEF";document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-r".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u9519\u8BEF";document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0072\u002D\u0072\u0061\u0074\u0069\u006F\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="\u9519\u8BEF";}else{var _0x1eag;const _0xbb_0xe83=_0xbbf3de['\u004E\u005F\u0074\u006F\u0074\u0061\u006C'];_0x1eag=(595590^595584)+(118125^118127);var _0xa1e2f=(783564^783564)+(535609^535611);const _0x06c93c=_0xbbf3de['\u0066\u0065\u0065\u0064\u005F\u0073\u0074\u0061\u0067\u0065'];_0xa1e2f='\u006A\u006B\u0064\u006A\u006D\u006D';const _0x92e8ac=_0x06c93c>(280549^280549)?_0x06c93c-(364915^364914):647245^647245;var _0x5_0x219;const _0x5ga=_0xbb_0xe83-_0x06c93c+(691336^691337);_0x5_0x219=(613207^613201)+(719214^719212);document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-latot-n".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0xbb_0xe83['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']();document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0066\u0065\u0065\u0064\u002D\u0073\u0074\u0061\u0067\u0065\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x06c93c['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']();document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006E\u002D\u0072\u0065\u0063\u0074\u0069\u0066\u0079\u0069\u006E\u0067\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x92e8ac['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']();document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u006E\u002D\u0073\u0074\u0072\u0069\u0070\u0070\u0069\u006E\u0067\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x5ga['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']();document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0072\u002D\u0076\u0061\u006C\u0075\u0065")['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=R['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](549355^549353);if(!isNaN(_0xaaa6a['\u0052\u006D\u0069\u006E'])&&isFinite(_0xaaa6a['\u0052\u006D\u0069\u006E'])&&_0xaaa6a['\u0052\u006D\u0069\u006E']>(624759^624759)){const _0x78f1d=R/_0xaaa6a['\u0052\u006D\u0069\u006E'];document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-oitar-r".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']=_0x78f1d['\u0074\u006F\u0046\u0069\u0078\u0065\u0064'](921083^921081);}else{document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("eulav-oitar-r".split("").reverse().join(""))['\u0074\u0065\u0078\u0074\u0043\u006F\u006E\u0074\u0065\u006E\u0074']="A/N".split("").reverse().join("");}plotMcCabeThieleWithSteps(_0xdee63d,_0x6_0xca5,_0x5bf,q,R,_0xbbf3de,_0xaaa6a);}});});