/* COFzyme格致网站通用样式 - 极简高级感设计 */

:root {
    --primary-blue: #0072B2;
    --primary-orange: #D55E00;
    --dark-blue: #006699;
    --light-blue: #f0f9ff;
    --text-primary: #0f172a;
    --text-secondary: #334155;
    --text-muted: #64748b;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
    color: var(--text-primary);
    line-height: 1.7;
    font-size: 16px;
    margin: 0;
    padding: 0;
    font-weight: 400;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-orange), #c44a00);
}

/* 头部导航样式 */
.header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(24px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: var(--spacing-md) 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-2xl);
    border-bottom-color: rgba(0, 114, 178, 0.1);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.header:hover::before {
    opacity: 1;
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 12px;
    text-decoration: none;
    display: inline-block;
    letter-spacing: -0.01em;
}

.nav-link:hover {
    color: var(--primary-blue);
    background: rgba(0, 114, 178, 0.06);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
    border-radius: 1px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 70%;
}

.nav-link.active {
    color: var(--primary-blue);
    background: rgba(0, 114, 178, 0.1);
    font-weight: 600;
}

/* 卡片样式 */
.card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 20;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: rgba(0, 114, 178, 0.1);
}

.card:hover::before {
    opacity: 1;
}

/* 按钮样式 */
.btn {
    background: linear-gradient(135deg, var(--primary-blue), #0056b3);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
    text-align: center;
    border: none;
    outline: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover {
    background: linear-gradient(135deg, var(--primary-orange), #c44a00);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    color: white;
    text-decoration: none;
}

.btn:hover::before {
    left: 100%;
}

/* 研究标签样式 */
.research-tag {
    display: inline-block;
    background: linear-gradient(135deg, var(--light-blue), #dbeafe);
    color: var(--primary-blue);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin-right: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 114, 178, 0.1);
    cursor: pointer;
}

.research-tag:hover {
    background: linear-gradient(135deg, var(--primary-blue), #0056b3);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-blue);
}

/* 页面标题样式 */
.page-title {
    text-align: center;
    margin-bottom: 3rem;
}

.page-title h1 {
    font-size: 3rem;
    font-weight: bold;
    background: linear-gradient(135deg, var(--dark-blue), var(--primary-blue));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

.page-title .divider {
    width: 4rem;
    height: 0.25rem;
    background: linear-gradient(90deg, var(--primary-orange), #ff6b35);
    margin: 0 auto;
    border-radius: 2px;
}

/* 页脚样式 */
.footer {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #1a202c 100%);
    color: white;
    padding: 3rem 0;
    margin-top: 4rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
    animation: shimmer 4s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.footer-content {
    text-align: center;
    position: relative;
    z-index: 10;
}

.footer-logo {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.footer-logo .orange {
    color: var(--primary-orange);
}

.footer-logo .blue {
    color: var(--primary-blue);
}

.footer-text {
    color: #a0aec0;
    font-size: 1.125rem;
}

.footer-dots {
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.footer-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.footer-dot:nth-child(1) {
    background-color: var(--primary-blue);
}

.footer-dot:nth-child(2) {
    background-color: var(--primary-orange);
    animation-delay: 0.5s;
}

.footer-dot:nth-child(3) {
    background-color: var(--primary-blue);
    animation-delay: 1s;
}

/* 动画效果 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
}

.celebration-emoji {
    font-size: 1.1em;
    margin: 0 0.2rem;
    display: inline-block;
    animation: bounce 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-title h1 {
        font-size: 2rem;
    }
    
    .nav-link {
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}
