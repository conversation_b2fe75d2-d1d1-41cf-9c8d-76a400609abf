<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 优化的脚本加载 -->
    <!-- 使用更轻量的 Chart.js 替代 Plotly.js 进行基础绘图 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js" defer></script>
    <!-- 保留 Math.js 用于数学计算 -->
    <script src="https://cdn.jsdelivr.net/npm/mathjs@11.8.0/lib/browser/math.min.js" defer></script>

    <!-- 预加载 Plotly.js，但不阻塞页面渲染 -->
    <link rel="preload" href="https://cdn.plot.ly/plotly-2.24.1.min.js" as="script">
    <script>
        // 异步加载 Plotly.js
        window.addEventListener('load', function() {
            const script = document.createElement('script');
            script.src = 'https://cdn.plot.ly/plotly-2.24.1.min.js';
            script.async = true;
            document.head.appendChild(script);
        });
    </script>
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .btn {
            background-color: #0072B2;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn:hover {
            background-color: #D55E00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 500;
            font-size: 0.875rem;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            transition: border-color 0.3s ease;
            font-size: 0.875rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #0072B2;
            box-shadow: 0 0 0 3px rgba(0, 114, 178, 0.1);
        }

        .result-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
        }

        .result-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: #0072B2;
            text-align: center;
            margin: 0.5rem 0;
        }

        .section-header {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0072B2;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: 0 0.375rem 0.375rem 0;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #0072B2;
            margin: 0;
        }

        .compact-grid {
            display: grid;
            gap: 0.75rem;
        }

        .input-hint {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .chart-container {
            height: 500px;
            margin: 1.5rem 0;
        }

        .formula {
            font-family: 'Times New Roman', Times, serif;
            font-style: italic;
            background-color: #f0f0f0;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            display: inline-block;
            margin: 0.5rem 0;
        }

        .tool-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .tool-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #0072B2;
        }

        .tab-content {
            display: none;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .tab-content.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .tool-highlight {
            animation: highlightPulse 1.5s ease-in-out;
            border: 2px solid #0072B2 !important;
            border-radius: 0.5rem;
        }

        @keyframes highlightPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 114, 178, 0.7);
                border-color: #0072B2;
            }
            50% {
                box-shadow: 0 0 0 15px rgba(0, 114, 178, 0);
                border-color: #D55E00;
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 114, 178, 0);
                border-color: #0072B2;
            }
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.25rem 0.25rem 0 0;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            border-bottom: 3px solid #0072B2;
            background-color: white;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link">首页</a>
                <a href="profile.html" class="nav-link">个人</a>
                <a href="research.html" class="nav-link">科研</a>
                <a href="teaching.html" class="nav-link">教学</a>
                <a href="members.html" class="nav-link">成员</a>
                <a href="tool.html" class="nav-link active">工具</a>
                <a href="downloads.html" class="nav-link">下载</a>
                <a href="index-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                程序计算
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="mb-8">
            <p class="text-center text-lg mb-6">本人开发了基于 Python/JavaScript 的化工原理实验计算工具，该工具能够对实验数据进行高效、精确的计算与分析，并通过直观的可视化呈现，从而深化学生对核心概念的理解与认知。</p>

            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3 mb-6 rounded-r-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            <strong>使用提示：</strong>点击下方工具卡片开始使用。如遇异常可尝试强制刷新：Windows <kbd class="bg-yellow-200 px-1 rounded text-xs">Ctrl+F5</kbd> / Mac <kbd class="bg-yellow-200 px-1 rounded text-xs">Cmd+Shift+R</kbd>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具选择卡片 -->
        <div class="grid md:grid-cols-3 gap-8 mb-12">
            <div class="card p-6 text-center tool-card" id="distillation-card" onclick="showTool('distillation')">
                <div class="tool-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">精馏计算</h3>
                <p class="text-gray-600">基于McCabe-Thiele方法计算乙醇-水精馏过程，确定理论塔板数、最小回流比等参数并绘制操作图</p>
            </div>

            <div class="card p-6 text-center tool-card" id="absorption-card" onclick="showTool('absorption')">
                <div class="tool-icon">
                    <i class="fas fa-vial"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">吸收计算</h3>
                <p class="text-gray-600">基于Herry定律计算低浓度CO2在水中的吸收过程，确定传质单元数、吸收率等参数并绘制操作线图</p>
            </div>

            <div class="card p-6 text-center tool-card" id="drying-card" onclick="showTool('drying')">
                <div class="tool-icon">
                    <i class="fas fa-wind"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">干燥计算</h3>
                <p class="text-gray-600">分析恒定干燥实验数据，计算干燥速率曲线，识别恒速段和降速段并进行拟合分析</p>
            </div>
        </div>

        <!-- 工具内容区域 -->
        <div class="tab-content active" id="distillation-tool">
            <div class="card p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">McCabe-Thiele 精馏计算</h2>

                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 左侧栏：输入参数和计算结果 -->
                    <div>
                        <!-- 输入参数部分 -->
                        <div class="mb-4">
                            <div class="section-header">
                                <h3 class="section-title">输入参数</h3>
                            </div>
                            <form id="distillation-form">
                                <!-- 组成参数 -->
                                <div class="compact-grid grid-cols-3 mb-3">
                                    <div class="form-group">
                                        <label class="form-label" for="xD">塔顶组成 (xD)</label>
                                        <input type="number" id="xD" class="form-input" placeholder="0.7" step="0.01" min="0" max="1" value="0.7">
                                        <p class="input-hint">乙醇摩尔分率 [0-1]</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="xW">塔底组成 (xW)</label>
                                        <input type="number" id="xW" class="form-input" placeholder="0.02" step="0.01" min="0" max="1" value="0.02">
                                        <p class="input-hint">乙醇摩尔分率 [0-1]</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="xF">进料组成 (xF)</label>
                                        <input type="number" id="xF" class="form-input" placeholder="0.3" step="0.01" min="0" max="1" value="0.3">
                                        <p class="input-hint">乙醇摩尔分率 [0-1]</p>
                                    </div>
                                </div>

                                <!-- 操作参数 -->
                                <div class="compact-grid grid-cols-2 mb-3">
                                    <div class="form-group">
                                        <label class="form-label" for="tF">进料温度 (tF)</label>
                                        <input type="number" id="tF" class="form-input" placeholder="20" step="0.1" value="20">
                                        <p class="input-hint">单位：°C</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="R">回流比 (R)</label>
                                        <input type="number" id="R" class="form-input" placeholder="3.0" step="0.1" min="0.1" value="3.0">
                                        <p class="input-hint">必须 > 0</p>
                                    </div>
                                </div>

                                <!-- 计算按钮 -->
                                <button type="button" id="calculate-btn" class="btn w-full">计算并绘图</button>
                            </form>
                        </div>

                        <!-- 计算结果部分 -->
                        <div class="mt-4">
                            <div class="section-header">
                                <h3 class="section-title">计算结果</h3>
                            </div>
                            <div class="result-box">
                                <!-- 基础参数 -->
                                <div class="compact-grid grid-cols-3 mb-3">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">进料热状况参数 (q)</p>
                                        <div class="result-value text-lg" id="q-value">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">最小回流比 (Rmin)</p>
                                        <div class="result-value text-lg" id="rmin-value">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">最小理论板数 (Nmin)</p>
                                        <div class="result-value text-lg" id="nmin-value">--</div>
                                    </div>
                                </div>

                                <!-- 板数参数 -->
                                <div class="compact-grid grid-cols-3 mb-3">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">总理论板数</p>
                                        <div class="result-value text-lg" id="n-total-value">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">精馏段板数</p>
                                        <div class="result-value text-lg" id="n-rectifying-value">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">提馏段板数</p>
                                        <div class="result-value text-lg" id="n-stripping-value">--</div>
                                    </div>
                                </div>

                                <!-- 操作参数 -->
                                <div class="compact-grid grid-cols-3">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">加料板位置</p>
                                        <div class="result-value text-lg" id="feed-stage-value">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">操作回流比</p>
                                        <div class="result-value text-lg" id="r-value">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">R/Rmin 比值</p>
                                        <div class="result-value text-lg" id="r-ratio-value">--</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧栏：McCabe-Thiele 图 -->
                    <div class="flex flex-col h-full">
                        <h3 class="text-xl font-semibold mb-4 text-[#0072B2] text-center">McCabe-Thiele 图</h3>
                        <div class="chart-wrapper flex-1 border-2 border-gray-200 rounded-lg bg-gradient-to-br from-blue-50 to-white shadow-sm hover:shadow-md transition-shadow duration-300 mx-4">
                            <div id="mccabe-plot" class="chart-container w-full h-full flex items-center justify-center p-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="absorption-tool">
            <div class="card p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">CO2-水吸收计算</h2>

                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 左侧栏：输入参数和计算结果 -->
                    <div>
                        <!-- 输入参数部分 -->
                        <div class="mb-4">
                            <div class="section-header">
                                <h3 class="section-title">输入参数</h3>
                            </div>
                            <form id="absorption-form">
                                <!-- 流量参数 -->
                                <div class="compact-grid grid-cols-3 mb-3">
                                    <div class="form-group">
                                        <label class="form-label" for="abs-air-flow">空气流量 (V1)</label>
                                        <input type="number" id="abs-air-flow" class="form-input" placeholder="0.3" step="0.1" min="0" value="0.3">
                                        <p class="input-hint">单位：m³/h</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="abs-water-flow">水流量</label>
                                        <input type="number" id="abs-water-flow" class="form-input" placeholder="300" step="1" min="0" value="300">
                                        <p class="input-hint">单位：L/h</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="abs-pressure">系统总压 (P)</label>
                                        <input type="number" id="abs-pressure" class="form-input" placeholder="101.325" step="0.1" min="0" value="101.325">
                                        <p class="input-hint">单位：kPa</p>
                                    </div>
                                </div>

                                <!-- 温度参数 -->
                                <div class="compact-grid grid-cols-2 mb-3">
                                    <div class="form-group">
                                        <label class="form-label" for="abs-air-temp">空气温度</label>
                                        <input type="number" id="abs-air-temp" class="form-input" placeholder="20" step="0.1" value="20">
                                        <p class="input-hint">单位：°C</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="abs-water-temp">水温</label>
                                        <input type="number" id="abs-water-temp" class="form-input" placeholder="25" step="0.1" value="25">
                                        <p class="input-hint">单位：°C</p>
                                    </div>
                                </div>

                                <!-- CO2浓度参数 -->
                                <div class="compact-grid grid-cols-2 mb-3">
                                    <div class="form-group">
                                        <label class="form-label" for="abs-co2-inlet">入口CO2浓度</label>
                                        <input type="number" id="abs-co2-inlet" class="form-input" placeholder="6" step="0.01" min="0" max="100" value="6">
                                        <p class="input-hint">单位：%</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="abs-co2-outlet">出口CO2浓度</label>
                                        <input type="number" id="abs-co2-outlet" class="form-input" placeholder="3" step="0.01" min="0" max="100" value="3">
                                        <p class="input-hint">单位：%</p>
                                    </div>
                                </div>

                                <!-- 计算按钮 -->
                                <button type="button" id="absorption-calculate-btn" class="btn w-full">计算并绘图</button>
                            </form>
                        </div>

                        <!-- 计算结果部分 -->
                        <div class="mt-4">
                            <div class="section-header">
                                <h3 class="section-title">计算结果</h3>
                            </div>
                            <div class="result-box">
                                <!-- 通量参数 -->
                                <div class="compact-grid grid-cols-3 mb-3">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">气相摩尔通量 (V)</p>
                                        <div class="result-value text-base" id="abs-v-flux">--</div>
                                        <p class="text-xs text-gray-500">kmol/(m²·h)</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">液相摩尔通量 (L)</p>
                                        <div class="result-value text-base" id="abs-l-flux">--</div>
                                        <p class="text-xs text-gray-500">kmol/(m²·h)</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">液气比 (L/V)</p>
                                        <div class="result-value text-base" id="abs-lv-ratio">--</div>
                                    </div>
                                </div>

                                <!-- 平衡参数 -->
                                <div class="compact-grid grid-cols-3 mb-3">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">亨利常数因子 (E)</p>
                                        <div class="result-value text-base">
                                            <span id="abs-henry-e">--</span>
                                            <span class="text-xs text-gray-500 ml-1">kPa</span>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">平衡常数 (m)</p>
                                        <div class="result-value text-base" id="abs-equilibrium-m">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">吸收因子 (A)</p>
                                        <div class="result-value text-base" id="abs-absorption-factor">--</div>
                                    </div>
                                </div>

                                <!-- 传质参数 -->
                                <div class="compact-grid grid-cols-3">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">传质单元数 (NOL)</p>
                                        <div class="result-value text-base" id="abs-nol">--</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">传质单元高度 (HOL)</p>
                                        <div class="result-value text-base">
                                            <span id="abs-hol">--</span>
                                            <span class="text-xs text-gray-500 ml-1">m</span>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">CO2吸收率</p>
                                        <div class="result-value text-base">
                                            <span id="abs-absorption-rate">--</span>
                                            <span class="text-xs text-gray-500 ml-1">%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧栏：图表和设备参数 -->
                    <div class="flex flex-col h-full">
                        <h3 class="text-xl font-semibold mb-4 text-[#0072B2] text-center">操作线与平衡线图</h3>
                        <div class="chart-wrapper flex-1 mb-4 border-2 border-gray-200 rounded-lg bg-gradient-to-br from-blue-50 to-white shadow-sm hover:shadow-md transition-shadow duration-300 mx-4">
                            <div id="absorption-plot" class="chart-container w-full h-full flex items-center justify-center p-4" style="min-height: 300px;"></div>
                        </div>

                        <!-- 设备参数部分 - 与左侧计算结果底部对齐 -->
                        <div class="mt-auto">
                            <div class="section-header">
                                <h3 class="section-title">设备参数</h3>
                            </div>
                            <div class="result-box">
                                <div class="compact-grid grid-cols-2 mb-2">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">入口液相摩尔比 (X2)</p>
                                        <div class="result-value text-base">0.0</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">填料高度 (Z)</p>
                                        <div class="result-value text-base">1.3 m</div>
                                    </div>
                                </div>
                                <div class="compact-grid grid-cols-2">
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">塔截面积 (Ω)</p>
                                        <div class="result-value text-base">0.00785 m²</div>
                                    </div>
                                    <div class="text-center">
                                        <p class="font-medium text-xs text-gray-600 mb-1">塔径 (D)</p>
                                        <div class="result-value text-base">0.1 m</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="drying-tool">
            <div class="card p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">干燥曲线分析</h2>

                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 左侧栏：实验数据输入 -->
                    <div class="flex flex-col h-full">
                        <!-- 实验常数部分 -->
                        <div class="mb-4">
                            <div class="section-header">
                                <h3 class="section-title">实验常数</h3>
                            </div>
                            <div class="compact-grid grid-cols-3">
                                <div class="form-group">
                                    <label class="form-label" for="dry-gc">干物料质量 (Gc)</label>
                                    <input type="number" id="dry-gc" class="form-input" placeholder="20" step="0.1" min="0" value="20">
                                    <p class="input-hint">单位：g</p>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="dry-surface">表面积 (S)</label>
                                    <input type="number" id="dry-surface" class="form-input" placeholder="308" step="1" min="0" value="308">
                                    <p class="input-hint">单位：cm²</p>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="dry-delta-w">质量间隔 (ΔW)</label>
                                    <input type="number" id="dry-delta-w" class="form-input" placeholder="2.0" step="0.1" min="0" value="2.0">
                                    <p class="input-hint">单位：g</p>
                                </div>
                            </div>
                        </div>

                        <!-- 实验数据输入部分 - 与右侧底部对齐 -->
                        <div class="flex-1 flex flex-col">
                            <div class="section-header">
                                <h3 class="section-title">实验数据 (Gi vs Δτ)</h3>
                            </div>

                            <!-- 数据集选择 -->
                            <div class="mb-3">
                                <label class="form-label" for="dry-dataset">数据集选择</label>
                                <div class="flex gap-2">
                                    <select id="dry-dataset" class="form-input flex-1">
                                        <option value="default">默认示例</option>
                                        <option value="dataset1">数据集 1</option>
                                        <option value="dataset2">数据集 2</option>
                                        <option value="custom">自定义数据</option>
                                    </select>
                                    <button type="button" id="dry-load-dataset" class="btn px-3 py-1 text-sm">加载</button>
                                </div>
                            </div>

                            <!-- 数据输入区域 - 自适应高度 -->
                            <div class="form-group mb-3 flex-1 flex flex-col">
                                <label class="form-label">原始数据</label>
                                <textarea id="dry-raw-data" class="form-input text-sm flex-1" style="min-height: 200px; resize: vertical;" placeholder="65 0&#10;63 165&#10;61 169&#10;...">65 0
63 165
61 169
59 164
57 163
55 160
53 177
51 189
49 228
47 237
45 280
43 355</textarea>
                                <p class="input-hint">格式：湿物料质量(g) 时间间隔(s)，每行一个数据点</p>
                            </div>

                            <div class="flex gap-2 mt-auto">
                                <button type="button" id="dry-calculate-xu" class="btn flex-1 text-sm">计算X-U数据</button>
                                <button type="button" id="dry-clear-data" class="btn bg-gray-500 hover:bg-gray-600 px-3 text-sm">清空</button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧栏：图表和分析 -->
                    <div class="flex flex-col h-full">
                        <h3 class="text-xl font-semibold mb-3 text-[#0072B2] text-center">干燥速率曲线</h3>

                        <!-- 图表容器 - 添加轮廓线和美化 -->
                        <div class="chart-wrapper flex-1 mb-4 border-2 border-gray-200 rounded-lg bg-gradient-to-br from-blue-50 to-white shadow-sm hover:shadow-md transition-shadow duration-300 mx-4">
                            <div id="drying-plot" class="chart-container w-full h-full flex items-center justify-center p-4 rounded-lg" style="min-height: 280px;"></div>
                        </div>

                        <!-- 拟合模型选择和分析结果 - 左右布局 -->
                        <div class="grid grid-cols-2 gap-4 mt-auto">
                            <!-- 左侧：拟合模型选择 -->
                            <div class="bg-gradient-to-br from-green-50 to-white border border-green-200 rounded-lg p-4 shadow-sm">
                                <div class="section-header">
                                    <h3 class="section-title text-green-700">拟合模型选择</h3>
                                </div>
                                <div class="space-y-2 mb-3">
                                    <label class="flex items-center text-sm hover:bg-green-100 p-2 rounded transition-colors duration-200">
                                        <input type="radio" name="dry-model" value="power" class="mr-2 text-green-600" checked>
                                        <span class="text-gray-700">幂函数模型 (U = k(x-xe)^n)</span>
                                    </label>
                                    <label class="flex items-center text-sm hover:bg-green-100 p-2 rounded transition-colors duration-200">
                                        <input type="radio" name="dry-model" value="polynomial" class="mr-2 text-green-600">
                                        <span class="text-gray-700">多项式模型 (U = a + bx + cx²)</span>
                                    </label>
                                    <label class="flex items-center text-sm hover:bg-green-100 p-2 rounded transition-colors duration-200">
                                        <input type="radio" name="dry-model" value="linear" class="mr-2 text-green-600">
                                        <span class="text-gray-700">线性模型 (U = mx + c)</span>
                                    </label>
                                </div>

                                <div class="flex flex-col gap-2">
                                    <button type="button" id="dry-analyze" class="btn w-full text-sm bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 transition-all duration-200">分析选定模型</button>
                                    <button type="button" id="dry-compare" class="btn bg-emerald-600 hover:bg-emerald-700 border-emerald-600 hover:border-emerald-700 w-full text-sm transition-all duration-200">比较所有模型</button>
                                </div>
                            </div>

                            <!-- 右侧：分析结果 -->
                            <div class="bg-gradient-to-br from-blue-50 to-white border border-blue-200 rounded-lg p-4 shadow-sm">
                                <div class="section-header">
                                    <h3 class="section-title text-blue-700">分析结果</h3>
                                </div>
                                <div class="result-box bg-white border border-blue-100 rounded-md">
                                    <div id="dry-results" class="text-xs font-mono whitespace-pre-wrap text-gray-700 leading-relaxed">
请按照以下步骤操作：
1. 选择数据集或输入原始数据
2. 设置实验常数 (Gc, S, ΔW)
3. 点击"计算X-U数据"
4. 选择拟合模型并点击"分析选定模型"
5. 或点击"比较所有模型"查看所有拟合结果
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <!-- 加载本地计算脚本 -->
    <script src="assets/js/jshaman_distillation.js" defer></script>
    <script src="assets/js/jshaman_Absorption.js" defer></script>
    <script src="assets/js/jshaman_drying.js" defer></script>

    <script>
        function showTool(toolId) {
            // 隐藏所有工具内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的工具内容
            const targetTool = document.getElementById(toolId + '-tool');
            targetTool.classList.add('active');

            // 更新卡片样式
            document.querySelectorAll('.tool-card').forEach(card => {
                card.style.borderColor = 'transparent';
            });
            document.getElementById(toolId + '-card').style.borderColor = '#0072B2';

            // 添加突出效果
            setTimeout(() => {
                const toolCard = targetTool.querySelector('.card');
                if (toolCard) {
                    toolCard.classList.add('tool-highlight');

                    // 移除突出效果
                    setTimeout(() => {
                        toolCard.classList.remove('tool-highlight');
                    }, 1500);
                }

                // 平滑滚动到工具区域
                targetTool.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查 Plotly 是否已加载，如果没有则显示提示
            function checkPlotlyStatus() {
                if (typeof Plotly === 'undefined') {
                    // 显示加载状态提示
                    const statusDiv = document.createElement('div');
                    statusDiv.id = 'plotly-status';
                    statusDiv.className = 'bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r-lg';
                    statusDiv.innerHTML = `
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-clock text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    <strong>正在加载高级绘图库...</strong> 基础功能已可用，高级图表功能稍后启用。
                                </p>
                            </div>
                        </div>
                    `;

                    const container = document.querySelector('.mb-8');
                    container.appendChild(statusDiv);

                    // 定期检查 Plotly 是否加载完成
                    const checkInterval = setInterval(() => {
                        if (typeof Plotly !== 'undefined') {
                            statusDiv.remove();
                            clearInterval(checkInterval);
                        }
                    }, 1000);
                }
            }

            // 延迟检查，给页面渲染时间
            setTimeout(checkPlotlyStatus, 2000);

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
            console.log('工具页面加载完成');
        });
    </script>
</body>
</html>
