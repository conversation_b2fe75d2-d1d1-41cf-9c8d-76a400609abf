// 干燥计算工具 JavaScript 实现
// 基于 Dry.py 的逻辑

// --- 预定义数据集 ---
const PREDEFINED_DATASETS = {
    "default": "65 0\n63 165\n61 169\n59 164\n57 163\n55 160\n53 177\n51 189\n49 228\n47 237\n45 280\n43 355",
    "dataset1": "50.28 166\n48.28 142\n46.28 154\n44.28 151\n42.28 149\n40.28 153\n38.28 167\n36.28 167\n34.28 161\n32.28 208\n30.28 266",
    "dataset2": "143.5 0\n141.5 236\n139.5 232\n137.5 219\n135.5 222\n133.5 219\n131.5 223\n129.5 224\n127.5 242\n125.5 255\n123.5 292\n121.5 340"
};

// --- 全局变量 ---
let calculatedXData = null;
let calculatedUData = null;
let currentModelResults = {};

// --- 辅助函数 ---
function parseRawData(rawDataText) {
    // 解析原始数据文本
    const lines = rawDataText.trim().split('\n');
    const data = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line === '' || line.startsWith('#')) continue;

        const parts = line.split(/\s+/);
        if (parts.length >= 2) {
            const gi = parseFloat(parts[0]);
            const deltaTau = parseFloat(parts[1]);

            if (!isNaN(gi) && !isNaN(deltaTau)) {
                data.push([gi, deltaTau]);
            }
        }
    }

    return data;
}

function calculateXUFromRaw(rawDataText, gcG, sCm2, deltaWG) {
    // 从原始数据计算 X-U 数据
    try {
        if (gcG <= 0 || sCm2 <= 0 || deltaWG <= 0) {
            throw new Error("Gc, S(cm2), 和 ΔW 必须为正数");
        }

        const gcKg = gcG / 1000.0;
        const sM2 = sCm2 / 10000.0;

        const rawData = parseRawData(rawDataText);
        if (rawData.length < 2) {
            throw new Error("至少需要两个有效的数据行 (Gi, delta_tau)");
        }

        const giValues = rawData.map(row => row[0]);
        const deltaTauValues = rawData.map(row => row[1]);

        // 检查 Gi 值是否都大于等于 Gc
        for (let i = 0; i < giValues.length; i++) {
            if (giValues[i] < gcG - 1e-9) {
                throw new Error(`第 ${i + 1} 行的 Gi 值小于 Gc (${gcG.toFixed(2)}g)`);
            }
        }

        // 检查负的 delta_tau 值
        for (let i = 0; i < deltaTauValues.length; i++) {
            if (deltaTauValues[i] < 0) {
                throw new Error(`第 ${i + 1} 行发现负的 delta_tau 值`);
            }
        }

        // 计算 Xi 值
        const xiValues = giValues.map(gi => (gi - gcG) / gcG);

        const xAvList = [];
        const uList = [];
        const numIntervals = xiValues.length - 1;

        for (let i = 0; i < numIntervals; i++) {
            const xAv = (xiValues[i] + xiValues[i + 1]) / 2;
            xAvList.push(xAv);

            const deltaTauI = deltaTauValues[i + 1];
            if (deltaTauI <= 1e-9) {
                console.warn(`间隔 ${i + 1} 的 delta_tau 为零或接近零 (${deltaTauI} s)，跳过 U 计算`);
                uList.push(NaN);
                continue;
            }

            // 计算 U = (质量变化_kg / S_m2) / 时间_s = kg / m2 / s
            const u = (deltaWG / 1000.0) / (sM2 * deltaTauI);
            uList.push(u);

            // 检查质量下降一致性（可选）
            const actualMassDrop = giValues[i] - giValues[i + 1];
            if (Math.abs(actualMassDrop - deltaWG) > deltaWG * 0.20) {
                console.warn(`间隔 ${i + 1} 质量下降不一致。期望 ≈${deltaWG}g，实际 ${actualMassDrop.toFixed(2)}g`);
            }
        }

        // 过滤 NaN 值
        const validIndices = [];
        for (let i = 0; i < xAvList.length; i++) {
            if (!isNaN(uList[i]) && !isNaN(xAvList[i])) {
                validIndices.push(i);
            }
        }

        if (validIndices.length === 0) {
            throw new Error("没有找到有效的正 delta_tau 间隔");
        }

        const xAvArray = validIndices.map(i => xAvList[i]);
        const uArray = validIndices.map(i => uList[i]);

        return [xAvArray, uArray];

    } catch (error) {
        throw new Error(`输入数据错误: ${error.message}`);
    }
}

// --- 拟合模型函数 ---
function powerModel(x, k, n, xe) {
    // 幂函数模型: U = k * (x - xe)^n, 对于 x > xe，否则为 0
    return x.map(xi => xi > xe ? k * Math.pow(xi - xe, n) : 0);
}

function polynomialModel(x, a, b, c) {
    // 多项式模型: U = a + b*x + c*x^2
    return x.map(xi => a + b * xi + c * xi * xi);
}

function linearModel(x, m, c) {
    // 线性模型: U = m*x + c
    return x.map(xi => m * xi + c);
}

function calculateRSquared(yTrue, yPred) {
    // 计算 R2 值
    const yMean = yTrue.reduce((sum, y) => sum + y, 0) / yTrue.length;
    const ssTotal = yTrue.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
    const ssResidual = yTrue.reduce((sum, y, i) => sum + Math.pow(y - yPred[i], 2), 0);
    return 1 - (ssResidual / ssTotal);
}

function calculateRMSE(yTrue, yPred) {
    // 计算均方根误差
    const mse = yTrue.reduce((sum, y, i) => sum + Math.pow(y - yPred[i], 2), 0) / yTrue.length;
    return Math.sqrt(mse);
}

// --- 线性回归函数 ---
function linearRegression(x, y) {
    // 输入验证
    if (!x || !y || x.length === 0 || y.length === 0) {
        throw new Error("输入数据为空");
    }

    if (x.length !== y.length) {
        throw new Error("X和Y数据长度不匹配");
    }

    if (x.length < 2) {
        throw new Error("至少需要2个数据点进行线性回归");
    }

    const n = x.length;

    // 检查数据有效性
    for (let i = 0; i < n; i++) {
        if (!isFinite(x[i]) || !isFinite(y[i])) {
            throw new Error(`数据点 ${i} 包含无效值: x=${x[i]}, y=${y[i]}`);
        }
    }

    const sumX = x.reduce((sum, xi) => sum + xi, 0);
    const sumY = y.reduce((sum, yi) => sum + yi, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const denominator = n * sumXX - sumX * sumX;
    if (Math.abs(denominator) < 1e-12) {
        throw new Error("线性回归分母接近零，数据可能共线");
    }

    const slope = (n * sumXY - sumX * sumY) / denominator;
    const intercept = (sumY - slope * sumX) / n;

    if (!isFinite(slope) || !isFinite(intercept)) {
        throw new Error(`线性回归计算出无效参数: slope=${slope}, intercept=${intercept}`);
    }

    const yPred = x.map(xi => slope * xi + intercept);
    const rSquared = calculateRSquared(y, yPred);

    if (!isFinite(rSquared)) {
        throw new Error(`R²计算结果无效: ${rSquared}`);
    }

    return { slope, intercept, rSquared, yPred };
}

// --- 非线性拟合函数（简化版本）---
function fitPowerModel(x, y) {
    // 简化的幂函数拟合
    try {
        // 使用对数变换进行线性化拟合的初始估计
        const minX = Math.min(...x);
        let xe = Math.max(0, minX * 0.5);

        // 过滤掉 x <= xe 的点
        const validIndices = [];
        for (let i = 0; i < x.length; i++) {
            if (x[i] > xe && y[i] > 0) {
                validIndices.push(i);
            }
        }

        if (validIndices.length < 3) {
            throw new Error("幂函数拟合需要至少3个有效点");
        }

        const xValid = validIndices.map(i => x[i] - xe);
        const yValid = validIndices.map(i => y[i]);

        // 对数变换
        const logX = xValid.map(xi => Math.log(xi));
        const logY = yValid.map(yi => Math.log(yi));

        const logFit = linearRegression(logX, logY);
        const k = Math.exp(logFit.intercept);
        const n = logFit.slope;

        const yPred = x.map(xi => xi > xe ? k * Math.pow(xi - xe, n) : 0);
        const rSquared = calculateRSquared(y, yPred);

        return { k, n, xe, rSquared, yPred };

    } catch (error) {
        console.warn("幂函数拟合失败，回退到线性模型");
        const linearFit = linearRegression(x, y);
        return {
            k: linearFit.slope,
            n: 1,
            xe: -linearFit.intercept / linearFit.slope,
            rSquared: linearFit.rSquared,
            yPred: linearFit.yPred || x.map(xi => linearFit.slope * xi + linearFit.intercept),
            fallback: true
        };
    }
}

function fitPolynomialModel(x, y) {
    // 二次多项式拟合
    try {
        const n = x.length;

        // 构建矩阵方程 A * params = B
        let sumX = 0, sumX2 = 0, sumX3 = 0, sumX4 = 0;
        let sumY = 0, sumXY = 0, sumX2Y = 0;

        for (let i = 0; i < n; i++) {
            const xi = x[i];
            const yi = y[i];
            const xi2 = xi * xi;
            const xi3 = xi2 * xi;
            const xi4 = xi3 * xi;

            sumX += xi;
            sumX2 += xi2;
            sumX3 += xi3;
            sumX4 += xi4;
            sumY += yi;
            sumXY += xi * yi;
            sumX2Y += xi2 * yi;
        }

        // 解 3x3 线性方程组 (简化版本)
        const det = n * (sumX2 * sumX4 - sumX3 * sumX3) - sumX * (sumX * sumX4 - sumX2 * sumX3) + sumX2 * (sumX * sumX3 - sumX2 * sumX2);

        if (Math.abs(det) < 1e-10) {
            throw new Error("矩阵奇异，无法求解");
        }

        // 使用克拉默法则求解
        const detA = sumY * (sumX2 * sumX4 - sumX3 * sumX3) - sumXY * (sumX * sumX4 - sumX2 * sumX3) + sumX2Y * (sumX * sumX3 - sumX2 * sumX2);
        const detB = n * (sumXY * sumX4 - sumX2Y * sumX3) - sumY * (sumX * sumX4 - sumX2 * sumX3) + sumX2 * (sumX * sumX2Y - sumXY * sumX2);
        const detC = n * (sumX2 * sumX2Y - sumX3 * sumXY) - sumX * (sumX * sumX2Y - sumX2 * sumXY) + sumY * (sumX * sumX3 - sumX2 * sumX2);

        const a = detA / det;
        const b = detB / det;
        const c = detC / det;

        const yPred = x.map(xi => a + b * xi + c * xi * xi);
        const rSquared = calculateRSquared(y, yPred);

        // 计算 xe (多项式为零的正根)
        let xe = 0;
        if (Math.abs(c) > 1e-9) {
            const discriminant = b * b - 4 * a * c;
            if (discriminant >= 0) {
                const root1 = (-b + Math.sqrt(discriminant)) / (2 * c);
                const root2 = (-b - Math.sqrt(discriminant)) / (2 * c);
                const validRoots = [root1, root2].filter(root => root > 0 && root < Math.min(...x));
                xe = validRoots.length > 0 ? validRoots[0] : 0;
            }
        } else if (Math.abs(b) > 1e-9) {
            xe = -a / b;
        }

        return { a, b, c, xe, rSquared, yPred };

    } catch (error) {
        console.warn("多项式拟合失败，回退到线性模型");
        const linearFit = linearRegression(x, y);
        return {
            a: linearFit.intercept,
            b: linearFit.slope,
            c: 0,
            xe: -linearFit.intercept / linearFit.slope,
            rSquared: linearFit.rSquared,
            yPred: linearFit.yPred || x.map(xi => linearFit.slope * xi + linearFit.intercept),
            fallback: true
        };
    }
}

// --- 格式化显示值 ---
function formatDisplayValue(value) {
    if (value === null || value === undefined || isNaN(value)) return "N/A";
    if (!isFinite(value)) return value > 0 ? "∞" : "-∞";

    if (Math.abs(value) > 1e5 || (Math.abs(value) < 1e-3 && value !== 0)) {
        return value.toExponential(3);
    } else {
        return value.toFixed(4);
    }
}

// --- 主要分析函数 ---
function analyzeDryingData(xData, uData, modelType = 'linear') {
    const minConstantPoints = 3;
    const minFallingPointsRequired = 3;

    if (!xData || !uData || xData.length !== uData.length) {
        return { error: "无效的 X 或 U 数据" };
    }

    if (xData.length < minConstantPoints + minFallingPointsRequired) {
        return { error: `此拟合需要至少 ${minConstantPoints + minFallingPointsRequired} 个有效的 X-U 数据点 (找到 ${xData.length} 个)` };
    }

    try {
        // 按 X 降序排序数据
        const sortIndices = xData.map((_, i) => i).sort((a, b) => xData[b] - xData[a]);
        const xDataSorted = sortIndices.map(i => xData[i]);
        const uDataSorted = sortIndices.map(i => uData[i]);
        const nPoints = xDataSorted.length;

        console.log(`原始数据点数: ${nPoints}`);
        console.log(`X数据范围: [${Math.min(...xDataSorted).toFixed(4)}, ${Math.max(...xDataSorted).toFixed(4)}]`);
        console.log(`排序后的X数据:`, xDataSorted.map(x => x.toFixed(4)));

        let bestFitInfo = null;

        // 第一步：确定临界点位置，优先选择能包含更多降速段点的临界点
        let selectedCriticalPoint = null;
        let bestBackupPoint = null;
        let bestBackupR2 = -Infinity;

        // 从最小的恒速段开始，找到第一个能包含足够多降速段点的临界点
        for (let lastConstIdx = minConstantPoints - 1; lastConstIdx < nPoints - minFallingPointsRequired; lastConstIdx++) {
            const constIndices = Array.from({length: lastConstIdx + 1}, (_, i) => i);
            const ucTrial = constIndices.reduce((sum, i) => sum + uDataSorted[i], 0) / constIndices.length;
            const xcTrial = xDataSorted[lastConstIdx];

            // 策略1：选择所有小于临界点含水量的点
            let fallingIndices = [];
            let xFalling = [];
            let uFalling = [];

            for (let i = 0; i < nPoints; i++) {
                if (xDataSorted[i] < xcTrial) {
                    fallingIndices.push(i);
                    xFalling.push(xDataSorted[i]);
                    uFalling.push(uDataSorted[i]);
                }
            }

            console.log(`临界点候选 @X=${xcTrial.toFixed(4)}: 策略1找到 ${xFalling.length} 个小于临界点的点`);

            // 如果策略1的点数不够，使用策略2：选择临界点之后的所有点
            if (xFalling.length < minFallingPointsRequired) {
                console.log(`  策略1点数不足，尝试策略2`);

                fallingIndices = [];
                xFalling = [];
                uFalling = [];

                for (let i = lastConstIdx + 1; i < nPoints; i++) {
                    fallingIndices.push(i);
                    xFalling.push(xDataSorted[i]);
                    uFalling.push(uDataSorted[i]);
                }

                console.log(`  策略2选择了 ${xFalling.length} 个点`);
            }

            if (xFalling.length < minFallingPointsRequired) {
                console.log(`  跳过，降速段点数仍不足 (${xFalling.length} < ${minFallingPointsRequired})`);
                continue;
            }

            // 检查方差
            const xVar = xFalling.reduce((sum, x) => sum + x * x, 0) / xFalling.length - Math.pow(xFalling.reduce((sum, x) => sum + x, 0) / xFalling.length, 2);
            const uVar = uFalling.reduce((sum, u) => sum + u * u, 0) / uFalling.length - Math.pow(uFalling.reduce((sum, u) => sum + u, 0) / uFalling.length, 2);

            if (xVar < 1e-15 || uVar < 1e-15) {
                console.log(`  跳过，降速段数据方差不足`);
                continue;
            }

            // 验证这个临界点是否可行，要求R²大于0.9
            try {
                const quickFit = linearRegression(xFalling, uFalling);
                console.log(`  验证结果: 降速段点数=${xFalling.length}, R2=${quickFit.rSquared.toFixed(4)}`);
                console.log(`  降速段X范围: [${Math.min(...xFalling).toFixed(4)}, ${Math.max(...xFalling).toFixed(4)}]`);

                // 检查R²是否满足阈值要求
                const minR2Threshold = 0.9;
                if (quickFit.rSquared >= minR2Threshold) {
                    selectedCriticalPoint = {
                        lastConstIdx: lastConstIdx,
                        xcTrial: xcTrial,
                        ucTrial: ucTrial,
                        xFalling: xFalling,
                        uFalling: uFalling,
                        fallingIndices: fallingIndices
                    };

                    console.log(`✓ 选择临界点 @X=${xcTrial.toFixed(4)}，包含 ${xFalling.length} 个降速段点，R2=${quickFit.rSquared.toFixed(4)} >= ${minR2Threshold}`);
                    break; // 找到满足条件的就停止
                } else {
                    console.log(`  R2=${quickFit.rSquared.toFixed(4)} < ${minR2Threshold}，记录为备用选项`);

                    // 记录最佳备用选项
                    if (quickFit.rSquared > bestBackupR2) {
                        bestBackupR2 = quickFit.rSquared;
                        bestBackupPoint = {
                            lastConstIdx: lastConstIdx,
                            xcTrial: xcTrial,
                            ucTrial: ucTrial,
                            xFalling: xFalling,
                            uFalling: uFalling,
                            fallingIndices: fallingIndices
                        };
                    }
                }

            } catch (error) {
                console.log(`  验证失败: ${error.message}`);
                continue;
            }
        }

        // 如果没有找到满足R²≥0.9的临界点，使用最佳备用选项
        if (!selectedCriticalPoint) {
            if (bestBackupPoint) {
                selectedCriticalPoint = bestBackupPoint;
                console.log(`⚠ 未找到R²≥0.9的临界点，使用最佳备用选项 @X=${selectedCriticalPoint.xcTrial.toFixed(4)}，R2=${bestBackupR2.toFixed(4)}`);
            } else {
                return { error: `未能找到任何可行的临界点` };
            }
        }

        // 第二步：使用选定的临界点进行指定模型的拟合
        const { lastConstIdx, xcTrial, ucTrial, xFalling, uFalling, fallingIndices } = selectedCriticalPoint;

        try {
            let fitResult;
            let modelParams;
            let modelName;
            let xeTrial;

            // 根据模型类型进行拟合
            if (modelType === 'linear') {
                fitResult = linearRegression(xFalling, uFalling);
                modelParams = { slope: fitResult.slope, intercept: fitResult.intercept };
                modelName = "线性模型";
                xeTrial = -fitResult.intercept / fitResult.slope;
            } else if (modelType === 'power') {
                fitResult = fitPowerModel(xFalling, uFalling);
                if (fitResult.fallback) {
                    modelParams = { slope: fitResult.k, intercept: -fitResult.k * fitResult.xe };
                    modelName = "线性模型 (幂函数拟合失败)";
                    xeTrial = fitResult.xe;
                } else {
                    modelParams = { k: fitResult.k, n: fitResult.n, xe: fitResult.xe };
                    modelName = "幂函数模型";
                    xeTrial = fitResult.xe;
                }
            } else if (modelType === 'polynomial') {
                fitResult = fitPolynomialModel(xFalling, uFalling);
                if (fitResult.fallback) {
                    modelParams = { slope: fitResult.b, intercept: fitResult.a };
                    modelName = "线性模型 (多项式拟合失败)";
                    xeTrial = fitResult.xe;
                } else {
                    modelParams = { a: fitResult.a, b: fitResult.b, c: fitResult.c };
                    modelName = "多项式模型";
                    xeTrial = fitResult.xe;
                }
            }

            if (fitResult.slope !== undefined && fitResult.slope <= 1e-9) {
                return { error: `拟合失败：斜率非正 (${fitResult.slope})` };
            }

            const rmseValue = calculateRMSE(uFalling, fitResult.yPred);

            bestFitInfo = {
                splitIdx: lastConstIdx,
                uc: ucTrial,
                xc: xcTrial,
                xe: xeTrial,
                rSquared: fitResult.rSquared,
                rmse: rmseValue,
                modelParams: modelParams,
                modelType: modelType,
                modelName: modelName,
                numFittedPoints: xFalling.length,
                fallingIndices: fallingIndices,
                startFallingIdx: lastConstIdx + 1,
                yPred: fitResult.yPred,
                xFalling: xFalling,
                uFalling: uFalling
            };

        } catch (error) {
            return { error: `拟合失败: ${error.message}` };
        }

        if (bestFitInfo === null) {
            return { error: `未能找到成功的 ${modelType} 拟合` };
        }

        // 准备结果文本
        const numConstPointsFinal = bestFitInfo.splitIdx + 1;
        const resultsText = `分析结果 (${bestFitInfo.modelName}):\n` +
            `-----------------\n` +
            `恒速段: ${numConstPointsFinal} 点, UC = ${formatDisplayValue(bestFitInfo.uc)} kg/(m2·s)\n` +
            `降速段: ${bestFitInfo.numFittedPoints} 点, 所有 X < ${formatDisplayValue(bestFitInfo.xc)} 的点\n` +
            `临界含水率 (Xc): ${formatDisplayValue(bestFitInfo.xc)} kg/kg\n` +
            `平衡含水率 (Xe): ${formatDisplayValue(bestFitInfo.xe)} kg/kg\n` +
            `拟合优度 R2: ${formatDisplayValue(bestFitInfo.rSquared)}\n` +
            `均方根误差 RMSE: ${formatDisplayValue(bestFitInfo.rmse)}`;

        return {
            ...bestFitInfo,
            resultsText: resultsText,
            xDataSorted: xDataSorted,
            uDataSorted: uDataSorted
        };

    } catch (error) {
        return { error: `分析过程中发生错误: ${error.message}` };
    }
}

// --- 计算 X-U 数据 ---
function calculateXUData() {
    try {
        const gcG = parseFloat(document.getElementById('dry-gc').value);
        const sCm2 = parseFloat(document.getElementById('dry-surface').value);
        const deltaWG = parseFloat(document.getElementById('dry-delta-w').value);
        const rawDataText = document.getElementById('dry-raw-data').value;

        const [xArray, uArray] = calculateXUFromRaw(rawDataText, gcG, sCm2, deltaWG);

        calculatedXData = xArray;
        calculatedUData = uArray;

        // 显示计算的 X-U 数据
        let xuText = "计算的 X-U 数据:\n";
        xuText += "X_AV\t\tU (kg/(m2·s))\n";
        xuText += "------------------------\n";
        for (let i = 0; i < xArray.length; i++) {
            xuText += `${formatDisplayValue(xArray[i])}\t\t${formatDisplayValue(uArray[i])}\n`;
        }

        document.getElementById('dry-results').textContent = xuText;

        // 绘制初始图表
        plotDryingChart(xArray, uArray);

        return true;

    } catch (error) {
        alert(`计算错误: ${error.message}`);
        document.getElementById('dry-results').textContent = `错误: ${error.message}`;
        return false;
    }
}

// --- 分析选定模型 ---
function analyzeSelectedModel() {
    if (!calculatedXData || !calculatedUData) {
        alert("请先计算 X-U 数据");
        return;
    }

    const selectedModel = document.querySelector('input[name="dry-model"]:checked').value;
    const result = analyzeDryingData(calculatedXData, calculatedUData, selectedModel);

    if (result.error) {
        document.getElementById('dry-results').textContent = `错误: ${result.error}`;
        return;
    }

    currentModelResults[selectedModel] = result;
    document.getElementById('dry-results').textContent = result.resultsText;

    // 绘制带拟合线的图表
    plotDryingChartWithFit(calculatedXData, calculatedUData, result);
}

// --- 比较所有模型 ---
function compareAllModels() {
    if (!calculatedXData || !calculatedUData) {
        alert("请先计算 X-U 数据");
        return;
    }

    const models = ['linear', 'power', 'polynomial'];
    const results = {};

    let comparisonText = "模型比较结果:\n";
    comparisonText += "=======================\n\n";

    for (const model of models) {
        const result = analyzeDryingData(calculatedXData, calculatedUData, model);
        if (!result.error) {
            results[model] = result;
            comparisonText += `${result.modelName}:\n`;
            comparisonText += `  R2 = ${formatDisplayValue(result.rSquared)}\n`;
            comparisonText += `  RMSE = ${formatDisplayValue(result.rmse)}\n`;
            comparisonText += `  Xc = ${formatDisplayValue(result.xc)} kg/kg\n`;
            comparisonText += `  Xe = ${formatDisplayValue(result.xe)} kg/kg\n\n`;
        } else {
            comparisonText += `${model} 模型: 拟合失败 - ${result.error}\n\n`;
        }
    }

    // 找到最佳模型
    let bestModel = null;
    let bestRSquared = -Infinity;
    for (const [model, result] of Object.entries(results)) {
        if (result.rSquared > bestRSquared) {
            bestRSquared = result.rSquared;
            bestModel = model;
        }
    }

    if (bestModel) {
        comparisonText += `推荐模型: ${results[bestModel].modelName} (最高 R2 = ${formatDisplayValue(bestRSquared)})`;
    }

    document.getElementById('dry-results').textContent = comparisonText;

    // 绘制比较图表
    plotDryingComparisonChart(calculatedXData, calculatedUData, results);
}

// --- 绘图函数 ---
function plotDryingChart(xData, uData) {
    const plotDiv = document.getElementById('drying-plot');
    if (!plotDiv) return;

    const traces = [{
        x: xData,
        y: uData,
        type: 'scatter',
        mode: 'markers',
        name: '实验数据',
        marker: {
            color: 'blue',
            size: 8,
            symbol: 'circle'
        }
    }];

    const layout = {
        title: '干燥速率曲线',
        xaxis: {
            title: '平均含水率 X (kg/kg)',
            showgrid: true
        },
        yaxis: {
            title: '干燥速率 U (kg/(m2·s))',
            showgrid: true,
            tickformat: '.2e'
        },
        showlegend: true,
        font: {
            family: 'Noto Sans SC, Arial, sans-serif',
            size: 12
        },
        width: 600,
        height: 500,
        margin: {
            l: 60,
            r: 30,
            b: 60,
            t: 80,
            pad: 4
        }
    };

    Plotly.newPlot(plotDiv, traces, layout);
}

function plotDryingChartWithFit(xData, uData, fitResult) {
    const plotDiv = document.getElementById('drying-plot');
    if (!plotDiv) return;

    const traces = [{
        x: xData,
        y: uData,
        type: 'scatter',
        mode: 'markers',
        name: '实验数据',
        marker: {
            color: 'blue',
            size: 8,
            symbol: 'circle'
        }
    }];

    // 添加恒速段线
    const xMax = Math.max(...xData);
    const xcPlot = fitResult.xc < xMax ? fitResult.xc : xMax;
    traces.push({
        x: [xcPlot, xMax],
        y: [fitResult.uc, fitResult.uc],
        type: 'scatter',
        mode: 'lines',
        name: `恒速段 (UC=${formatDisplayValue(fitResult.uc)})`,
        line: {color: 'red', width: 2, dash: 'dash'}
    });

    // 添加降速段拟合线
    const xFalling = fitResult.xFalling || fitResult.fallingIndices.map(i => fitResult.xDataSorted[i]);
    const xFitMin = Math.min(...xFalling);
    const xFitMax = Math.max(...xFalling);
    const xFitRange = Array.from({length: 50}, (_, i) => xFitMin + (xFitMax - xFitMin) * i / 49);

    let yFitRange;
    if (fitResult.modelType === 'linear' || fitResult.modelParams.slope !== undefined) {
        const slope = fitResult.modelParams.slope;
        const intercept = fitResult.modelParams.intercept;
        yFitRange = xFitRange.map(x => slope * x + intercept);
    } else if (fitResult.modelType === 'power') {
        const { k, n, xe } = fitResult.modelParams;
        yFitRange = xFitRange.map(x => x > xe ? k * Math.pow(x - xe, n) : 0);
    } else if (fitResult.modelType === 'polynomial') {
        const { a, b, c } = fitResult.modelParams;
        yFitRange = xFitRange.map(x => a + b * x + c * x * x);
    }

    if (yFitRange) {
        traces.push({
            x: xFitRange,
            y: yFitRange,
            type: 'scatter',
            mode: 'lines',
            name: `${fitResult.modelName} (R2=${formatDisplayValue(fitResult.rSquared)})`,
            line: {color: 'green', width: 2}
        });
    }

    // 添加临界点标记
    traces.push({
        x: [fitResult.xc],
        y: [fitResult.uc],
        type: 'scatter',
        mode: 'markers',
        name: `临界点 (Xc=${formatDisplayValue(fitResult.xc)})`,
        marker: {color: 'black', size: 10, symbol: 'x'}
    });

    // 添加平衡点标记
    if (fitResult.xe > 0) {
        traces.push({
            x: [fitResult.xe],
            y: [0],
            type: 'scatter',
            mode: 'markers',
            name: `平衡点 (Xe=${formatDisplayValue(fitResult.xe)})`,
            marker: {color: 'red', size: 10, symbol: 'diamond'}
        });
    }

    const layout = {
        title: `干燥速率曲线 - ${fitResult.modelName}拟合`,
        xaxis: {
            title: '平均含水率 X (kg/kg)',
            showgrid: true
        },
        yaxis: {
            title: '干燥速率 U (kg/(m2·s))',
            showgrid: true,
            tickformat: '.2e'
        },
        showlegend: true,
        font: {
            family: 'Noto Sans SC, Arial, sans-serif',
            size: 12
        },
        width: 600,
        height: 500,
        margin: {
            l: 60,
            r: 30,
            b: 60,
            t: 80,
            pad: 4
        }
    };

    Plotly.newPlot(plotDiv, traces, layout);
}

function plotDryingComparisonChart(xData, uData, results) {
    const plotDiv = document.getElementById('drying-plot');
    if (!plotDiv) return;

    const traces = [{
        x: xData,
        y: uData,
        type: 'scatter',
        mode: 'markers',
        name: '实验数据',
        marker: {
            color: 'black',
            size: 8,
            symbol: 'circle'
        }
    }];

    const colors = { linear: 'red', power: 'green', polynomial: 'blue' };
    const linestyles = { linear: 'solid', power: 'dash', polynomial: 'dot' };

    for (const [modelType, result] of Object.entries(results)) {
        if (result.error) continue;

        const xFalling = result.xFalling || result.fallingIndices.map(i => result.xDataSorted[i]);
        const xFitMin = Math.min(...xFalling);
        const xFitMax = Math.max(...xFalling);
        const xFitRange = Array.from({length: 50}, (_, i) => xFitMin + (xFitMax - xFitMin) * i / 49);

        let yFitRange;
        if (modelType === 'linear' || result.modelParams.slope !== undefined) {
            const slope = result.modelParams.slope;
            const intercept = result.modelParams.intercept;
            yFitRange = xFitRange.map(x => slope * x + intercept);
        } else if (modelType === 'power') {
            const { k, n, xe } = result.modelParams;
            yFitRange = xFitRange.map(x => x > xe ? k * Math.pow(x - xe, n) : 0);
        } else if (modelType === 'polynomial') {
            const { a, b, c } = result.modelParams;
            yFitRange = xFitRange.map(x => a + b * x + c * x * x);
        }

        if (yFitRange) {
            traces.push({
                x: xFitRange,
                y: yFitRange,
                type: 'scatter',
                mode: 'lines',
                name: `${result.modelName} (R2=${formatDisplayValue(result.rSquared)})`,
                line: {
                    color: colors[modelType],
                    width: 2,
                    dash: linestyles[modelType]
                }
            });
        }
    }

    const layout = {
        title: '干燥速率曲线 - 模型比较',
        xaxis: {
            title: '平均含水率 X (kg/kg)',
            showgrid: true
        },
        yaxis: {
            title: '干燥速率 U (kg/(m2·s))',
            showgrid: true,
            tickformat: '.2e'
        },
        showlegend: true,
        font: {
            family: 'Noto Sans SC, Arial, sans-serif',
            size: 12
        },
        width: 600,
        height: 500,
        margin: {
            l: 60,
            r: 30,
            b: 60,
            t: 80,
            pad: 4
        }
    };

    Plotly.newPlot(plotDiv, traces, layout);
}

// --- 数据集加载函数 ---
function loadDataset() {
    const selectedDataset = document.getElementById('dry-dataset').value;
    const rawDataTextarea = document.getElementById('dry-raw-data');
    const gcInput = document.getElementById('dry-gc');
    const surfaceInput = document.getElementById('dry-surface');
    const deltaWInput = document.getElementById('dry-delta-w');

    if (selectedDataset === 'custom') {
        // 自定义数据，不改变文本区域内容
        return;
    }

    // 加载预定义数据集
    if (PREDEFINED_DATASETS[selectedDataset]) {
        rawDataTextarea.value = PREDEFINED_DATASETS[selectedDataset];

        // 根据数据集设置适当的常数
        if (selectedDataset === 'default') {
            gcInput.value = '20';
            surfaceInput.value = '308';
            deltaWInput.value = '2.0';
        } else if (selectedDataset === 'dataset1' || selectedDataset === 'dataset2') {
            gcInput.value = '25';
            surfaceInput.value = '300';
            deltaWInput.value = '2.0';
        }
    }
}

function clearRawData() {
    document.getElementById('dry-raw-data').value = '';
    document.getElementById('dry-results').textContent =
        '请按照以下步骤操作：\n1. 选择数据集或输入原始数据\n2. 设置实验常数 (Gc, S, ΔW)\n3. 点击"计算X-U数据"\n4. 选择拟合模型并点击"分析选定模型"\n5. 或点击"比较所有模型"查看所有拟合结果';

    // 清空图表
    const plotDiv = document.getElementById('drying-plot');
    if (plotDiv) {
        Plotly.purge(plotDiv);
    }

    // 重置全局变量
    calculatedXData = null;
    calculatedUData = null;
    currentModelResults = {};
}

// --- 事件监听器 ---
document.addEventListener('DOMContentLoaded', function() {
    // 数据集加载按钮
    const loadDatasetBtn = document.getElementById('dry-load-dataset');
    if (loadDatasetBtn) {
        loadDatasetBtn.addEventListener('click', loadDataset);
    }

    // 数据集选择下拉框
    const datasetSelect = document.getElementById('dry-dataset');
    if (datasetSelect) {
        datasetSelect.addEventListener('change', loadDataset);
    }

    // 计算 X-U 数据按钮
    const calculateXUBtn = document.getElementById('dry-calculate-xu');
    if (calculateXUBtn) {
        calculateXUBtn.addEventListener('click', calculateXUData);
    }

    // 清空数据按钮
    const clearDataBtn = document.getElementById('dry-clear-data');
    if (clearDataBtn) {
        clearDataBtn.addEventListener('click', clearRawData);
    }

    // 分析选定模型按钮
    const analyzeBtn = document.getElementById('dry-analyze');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', analyzeSelectedModel);
    }

    // 比较所有模型按钮
    const compareBtn = document.getElementById('dry-compare');
    if (compareBtn) {
        compareBtn.addEventListener('click', compareAllModels);
    }
});