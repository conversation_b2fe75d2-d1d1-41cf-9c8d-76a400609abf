<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.4), transparent);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.02), rgba(0, 102, 153, 0.02));
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .card:hover::before {
            opacity: 1;
            height: 3px;
        }

        .card:hover::after {
            opacity: 1;
        }

        .research-tag {
            display: inline-block;
            background: linear-gradient(135deg, var(--light-blue), #dbeafe);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin-right: 0.75rem;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(0, 114, 178, 0.1);
            cursor: pointer;
        }

        .research-tag:hover {
            background: linear-gradient(135deg, var(--primary-blue), #0056b3);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-blue);
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link">首页</a>
                <a href="profile.html" class="nav-link active">个人</a>
                <a href="research.html" class="nav-link">科研</a>
                <a href="teaching.html" class="nav-link">教学</a>
                <a href="members.html" class="nav-link">成员</a>
                <a href="tool.html" class="nav-link">工具</a>
                <a href="downloads.html" class="nav-link">下载</a>
                <a href="profile-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <!-- 个人简介内容 -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                个人介绍
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <div class="md:col-span-1">
                <div class="card p-6 text-center">
                    <div class="w-48 h-48 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/李致贤照片.jpg" alt="李致贤照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/200?text=照片'">
                    </div>
                    <h3 class="text-3xl text-[#006699] font-semibold mb-2">李致贤</h3>
                    <p class="text-3xl-gray-600 mb-4"> 副教授, 学术/专业硕导</p>
                    <p class="text-3xl-gray-600 mb-4"> 华南理工大学 化学与化工学院</p>


                    <div class="flex justify-center items-center space-x-6 mt-2">
                        <a href="https://www.scholarmate.com/P/eIvume" target="_blank" class="text-[#0072B2] hover:text-[#D55E00] flex items-center"><i class="fas fa-globe mr-2"></i>科研之友</a>
                        <a href="https://scholar.google.com/citations?user=Bbv7634AAAAJ&hl=zh-CN" target="_blank" class="text-[#0072B2] hover:text-[#D55E00] flex items-center"><i class="fab fa-google mr-2"></i>Google学术</a>
                    </div>
                </div>
            </div>
            <div class="md:col-span-2">
                <div class="card p-6">
                    <h3 class="text-2xl font-semibold mb-4 text-[#0072B2]">个人简介</h3>
                    <ul class="list-disc list-inside space-y-3 mb-6">
                        <li>本人长期致力于酶催化体系的仿生设计与化学改造研究，聚焦木质纤维素生物转化的关键科学问题，为可持续生物制造提供理论基础和技术支撑。</li>
                        <li>作为项目负责人主持国家自然科学基金、国家重点研发计划子课题等纵向科研项目。研究成果在ACS Catalysis等国际知名期刊发表SCI论文56篇，已获得中国发明专利授权9项及美国发明专利授权2项。</li>
                        <li>担任国家级线下一流课程主讲教师，系全国石油和化工行业优秀教学团队核心成员。承担本科生核心课程《化工原理》（中文及全英文）教学任务，年均授课时数超过300学时。在《化工高等教育》等专业期刊发表教学研究论文2篇，参与编写专业教材1部。</li>
                    </ul>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">教育经历</h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>2010.09-2016.01，清华大学，化学工程系，工学博士，导师：欧阳平凯、刘铮</li>
                        <li>2006.09-2010.07，南京工业大学，生物与制药工程学院，工学学士，班主任/导师：黄和</li>
                    </ul>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">工作经历</h3>
                    <ul class="list-disc list-inside space-y-2">
                        <li>2021.09 - 至今，华南理工大学，化学与化工学院，副教授</li>
                        <li>2018.09 - 2021.09，华南理工大学，化学与化工学院，讲师</li>
                        <li>2016.06 - 2018.09，华南理工大学，化学与化工学院，博士后</li>
                    </ul>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">研究兴趣</h3>
                    <div class="flex flex-wrap mb-4">
                        <span class="research-tag">木质素利用</span>
                        <span class="research-tag">仿生催化</span>
                        <span class="research-tag">酶催化</span>
                        <span class="research-tag">共价有机框架</span>

                    </div>

                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">所属团队</h3>
                    <p class="mb-2">生物质化工团队 <a  class="text-[#0072B2] hover:text-[#D55E00]">（负责人：邱学青）       </a></p>
                    <p class="mb-2">化工原理教学团队 <a  class="text-[#0072B2] hover:text-[#D55E00]">（负责人：郑大锋）</a></p>


                    <h3 class="text-2xl font-semibold mb-4 mt-6 text-[#0072B2]">联系方式</h3>
                    <p class="mb-2">Email：<a class="text-[#0072B2] hover:text-[#D55E00]"><EMAIL></a></p>
                    <p> 办公地址：华南理工大学五山校区化学与化工学院16号楼</p>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // 基本的页面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
            console.log('个人页面加载完成');

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
