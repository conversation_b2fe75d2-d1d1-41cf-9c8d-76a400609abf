// 精馏计算工具 JavaScript 实现
// 基于 Dist-Rmin.py 的逻辑

// --- 乙醇-水 VLE 数据 (摩尔分率) ---
const x_ethanol_data = [
    0.0, 0.0201, 0.0507, 0.0795, 0.1048, 0.1495, 0.2000, 0.2500,
    0.3001, 0.3509, 0.4000, 0.4541, 0.5016, 0.5400, 0.5955, 0.6405,
    0.7063, 0.7599, 0.7982, 0.8597, 0.8941, 1.0
]; // 液相乙醇摩尔分率
const y_ethanol_data = [
    0.0, 0.1838, 0.3306, 0.4018, 0.4461, 0.4977, 0.5309, 0.5548,
    0.5770, 0.5955, 0.6144, 0.6343, 0.6534, 0.6692, 0.6959, 0.7186,
    0.7582, 0.7926, 0.8183, 0.8640, 0.8941, 1.0
]; // 气相乙醇摩尔分率

// T-x 数据 (泡点曲线) 乙醇-水在大气压下
const x_Tx_data = [
    0.0, 0.05, 0.1, 0.15, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.894, 0.95, 1.0
]; // 液相乙醇摩尔分率
const T_Tx_data = [
    100.0, 89.0, 84.1, 82.3, 81.7, 80.4, 79.5, 78.9, 78.5, 78.3, 78.2, 78.15, 78.2, 78.3
]; // 泡点温度 (°C)

// 近似物理性质用于 q 值计算 (kJ/kmol 或 kJ/kmol/°C)
const R_LATENT_HEAT_ETHANOL_KJ_KMOL = 38560; // 乙醇汽化潜热
const R_LATENT_HEAT_WATER_KJ_KMOL = 40650;   // 水汽化潜热
const CP_LIQ_ETHANOL_KJ_KMOL_C = 112.0;      // 乙醇液体热容
const CP_LIQ_WATER_KJ_KMOL_C = 75.4;         // 水液体热容

// --- q 值计算函数 ---
function getBubblePointTemp(xF_mol) {
    // 计算给定液相摩尔分率 xF 的泡点温度
    if (!(x_Tx_data[0] <= xF_mol && xF_mol <= x_Tx_data[x_Tx_data.length-1])) {
        console.warn(`警告: 进料组成 xF=${xF_mol.toFixed(3)} 超出 T-x 数据范围 [${x_Tx_data[0].toFixed(3)}, ${x_Tx_data[x_Tx_data.length-1].toFixed(3)}] 用于泡点计算。结果为外插值，可能不准确。`);
    }

    // 线性插值
    let t_S = linearInterpolation(xF_mol, x_Tx_data, T_Tx_data);
    return t_S;
}

function getLatentHeatMixture(xF_mol) {
    // 计算进料混合物的近似汽化潜热
    const xF_clamped = Math.max(0.0, Math.min(parseFloat(xF_mol), 1.0));
    const r_F = xF_clamped * R_LATENT_HEAT_ETHANOL_KJ_KMOL + (1.0 - xF_clamped) * R_LATENT_HEAT_WATER_KJ_KMOL;
    return Math.abs(r_F) > 1e-9 ? r_F : 1e-9;
}

function getHeatCapacityMixture(xF_mol) {
    // 计算进料混合物的近似液体热容
    const xF_clamped = Math.max(0.0, Math.min(parseFloat(xF_mol), 1.0));
    const C_pF = xF_clamped * CP_LIQ_ETHANOL_KJ_KMOL_C + (1.0 - xF_clamped) * CP_LIQ_WATER_KJ_KMOL_C;
    return C_pF;
}

function calculateQValue(xF_mol, t_F) {
    // 计算进料热状况参数 'q'
    try {
        xF_mol = parseFloat(xF_mol);
        t_F = parseFloat(t_F);
        const xF_clamped = Math.max(0.0, Math.min(xF_mol, 1.0));

        if (xF_clamped !== xF_mol) {
            console.warn(`警告: 进料组成 xF=${xF_mol.toFixed(3)} 超出 [0, 1] 范围，已限制为 ${xF_clamped.toFixed(3)} 用于 q 值计算。`);
            xF_mol = xF_clamped;
        }

        const t_S = getBubblePointTemp(xF_mol);
        const temp_tolerance = 0.1;

        if (Math.abs(t_F - t_S) < temp_tolerance) {
            return 1.0; // 饱和液体
        }

        const r_F = getLatentHeatMixture(xF_mol);
        const C_pF = getHeatCapacityMixture(xF_mol);

        if (Math.abs(r_F) < 1e-9) {
            console.error("错误: 混合物计算得到的汽化潜热接近零。");
            return NaN;
        }

        const q = 1.0 + (C_pF * (t_S - t_F)) / r_F;
        return q;
    } catch (e) {
        console.error("计算 q 值错误:", e);
        return NaN;
    }
}

// --- McCabe-Thiele 计算逻辑 ---
function equilibriumY(x) {
    // 返回平衡气相摩尔分率 y，使用线性插值
    return linearInterpolation(x, x_ethanol_data, y_ethanol_data);
}

function equilibriumX(y) {
    // 返回平衡液相摩尔分率 x，使用线性插值
    return linearInterpolation(y, y_ethanol_data, x_ethanol_data);
}

function rectifyingOpLine(x, R, xD) {
    // 计算给定 x 在精馏段操作线上的 y 值
    R = parseFloat(R);
    xD = parseFloat(xD);

    if (R <= 0) return NaN;
    if (Math.abs(R + 1.0) < 1e-9) return NaN;

    return (R / (R + 1.0)) * x + (xD / (R + 1.0));
}

function qLineY(x, q, xF) {
    // 计算给定 x 在 q 线上的 y 值
    q = parseFloat(q);
    xF = parseFloat(xF);
    const TOL = 1e-9;

    if (Math.abs(q - 1.0) < TOL) {
        return NaN; // 垂直线，用于绘图时特殊处理
    } else if (Math.abs(q - 0.0) < TOL) {
        return xF;
    } else {
        const denom = q - 1.0;
        if (Math.abs(denom) < TOL) return NaN;
        return (q / denom) * x - (xF / denom);
    }
}

function findIntersection(R, xD, q, xF) {
    // 找到精馏段操作线和 q 线的交点 (x, y)
    R = parseFloat(R);
    xD = parseFloat(xD);
    q = parseFloat(q);
    xF = parseFloat(xF);
    const TOL = 1e-9;

    if (R <= 0) {
        console.error("错误: 回流比 R 必须大于 0 才能找到交点。");
        return [NaN, NaN];
    }

    if (Math.abs(R + 1.0) < TOL) {
        console.error("错误: R+1 接近于零。");
        return [NaN, NaN];
    }

    let x_intersect, y_intersect;

    if (Math.abs(q - 1.0) < TOL) {
        x_intersect = xF;
        y_intersect = rectifyingOpLine(x_intersect, R, xD);
    } else if (Math.abs(q - 0.0) < TOL) {
        y_intersect = xF;
        if (Math.abs(R) < TOL) {
            console.error("错误: R 接近于零，无法计算 q=0 时的交点 x 坐标。");
            return [NaN, NaN];
        }
        x_intersect = (y_intersect * (R + 1.0) - xD) / R;
    } else {
        const denom_q = q - 1.0;
        if (Math.abs(denom_q) < TOL) {
            console.error("错误: q 接近 1，无法计算 q 线参数。");
            return [NaN, NaN];
        }
        const coeff_q = q / denom_q;
        const coeff_R = R / (R + 1.0);
        const denominator = coeff_q - coeff_R;

        if (Math.abs(denominator) < TOL) {
            console.warn("警告：q 线和精馏段操作线平行或接近平行，无法找到唯一交点。");
            return [NaN, NaN];
        }

        const term1 = xD / (R + 1.0);
        const term2 = xF / denom_q;
        x_intersect = (term1 + term2) / denominator;
        y_intersect = rectifyingOpLine(x_intersect, R, xD);
    }

    // 限制在 [0, 1] 范围内
    x_intersect = Math.max(0.0, Math.min(x_intersect, 1.0));
    y_intersect = Math.max(0.0, Math.min(y_intersect, 1.0));

    return [x_intersect, y_intersect];
}

function strippingOpLineCoeffs(x_intersect, y_intersect, xW) {
    // 计算提馏段操作线的斜率和截距
    xW = parseFloat(xW);
    const TOL = 1e-9;

    if (isNaN(x_intersect) || isNaN(y_intersect)) {
        return [NaN, NaN];
    }

    if (Math.abs(x_intersect - xW) < TOL) {
        return [NaN, NaN];
    }

    const slope = (y_intersect - xW) / (x_intersect - xW);
    const intercept = xW - slope * xW;

    return [slope, intercept];
}

// --- 辅助函数 ---
function linearInterpolation(x, xArray, yArray) {
    // 线性插值函数
    if (x <= xArray[0]) return yArray[0];
    if (x >= xArray[xArray.length - 1]) return yArray[yArray.length - 1];

    for (let i = 0; i < xArray.length - 1; i++) {
        if (xArray[i] <= x && x <= xArray[i + 1]) {
            const t = (x - xArray[i]) / (xArray[i + 1] - xArray[i]);
            return yArray[i] * (1 - t) + yArray[i + 1] * t;
        }
    }

    return NaN; // 不应该到达这里
}

// --- 绘图函数 ---
function plotMcCabeThiele(xD, xW, xF, q, R) {
    // 创建 McCabe-Thiele 图（不含阶梯）
    const plotDiv = document.getElementById('mccabe-plot');

    // 生成平衡线数据
    const x_values = Array.from({length: 101}, (_, i) => i / 100);
    const y_eq_values = x_values.map(x => equilibriumY(x));

    // 计算操作线交点
    const [x_intersect, y_intersect] = findIntersection(R, xD, q, xF);

    // 计算提馏段操作线参数
    const [strip_slope, strip_intercept] = strippingOpLineCoeffs(x_intersect, y_intersect, xW);

    // 创建图表数据
    const traces = [
        // 对角线 y=x
        {
            x: [0, 1],
            y: [0, 1],
            type: 'scatter',
            mode: 'lines',
            name: 'y = x',
            line: {color: 'black', width: 1}
        },
        // 平衡线
        {
            x: x_values,
            y: y_eq_values,
            type: 'scatter',
            mode: 'lines',
            name: '平衡线',
            line: {color: 'blue', width: 2}
        },
        // VLE 数据点
        {
            x: x_ethanol_data,
            y: y_ethanol_data,
            type: 'scatter',
            mode: 'markers',
            name: 'VLE 数据点',
            marker: {color: 'blue', size: 6, opacity: 0.7}
        },
        // 精馏段操作线
        {
            x: [x_intersect, xD],
            y: [y_intersect, xD],
            type: 'scatter',
            mode: 'lines',
            name: `精馏段操作线 (R=${R.toFixed(2)})`,
            line: {color: 'red', width: 2}
        },
        // 提馏段操作线
        {
            x: [xW, x_intersect],
            y: [xW, y_intersect],
            type: 'scatter',
            mode: 'lines',
            name: '提馏段操作线',
            line: {color: 'green', width: 2}
        },
        // 关键点
        {
            x: [xD, xW, xF, x_intersect],
            y: [xD, xW, xF, y_intersect],
            type: 'scatter',
            mode: 'markers',
            name: '关键点 xD',
            marker: {
                color: ['red', 'green', 'purple', 'black'],
                size: 10,
                symbol: ['circle', 'circle', 'circle', 'circle-open']
            },
            hoverinfo: 'none'
        }
    ];

    // 添加 q 线
    let q_line_trace;
    const TOL = 1e-9;

    if (Math.abs(q - 1.0) < TOL) {
        // 垂直 q 线
        q_line_trace = {
            x: [xF, xF],
            y: [xF, y_intersect],
            type: 'scatter',
            mode: 'lines',
            name: `q 线 (q=${q.toFixed(2)})`,
            line: {color: 'purple', width: 2, dash: 'dash'}
        };
    } else if (Math.abs(q - 0.0) < TOL) {
        // 水平 q 线
        q_line_trace = {
            x: [x_intersect, xF],
            y: [xF, xF],
            type: 'scatter',
            mode: 'lines',
            name: `q 线 (q=${q.toFixed(2)})`,
            line: {color: 'purple', width: 2, dash: 'dash'}
        };
    } else {
        // 斜 q 线
        q_line_trace = {
            x: [xF, x_intersect],
            y: [xF, y_intersect],
            type: 'scatter',
            mode: 'lines',
            name: `q 线 (q=${q.toFixed(2)})`,
            line: {color: 'purple', width: 2, dash: 'dash'}
        };
    }

    traces.push(q_line_trace);

    // 添加从关键点到x轴的垂直虚线
    const verticalLines = [
        // xD 垂线
        {
            x: [xD, xD],
            y: [0, xD],
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {color: 'red', width: 1, dash: 'dot'},
            showlegend: false,
            hoverinfo: 'none'
        },
        // xW 垂线
        {
            x: [xW, xW],
            y: [0, xW],
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {color: 'green', width: 1, dash: 'dot'},
            showlegend: false,
            hoverinfo: 'none'
        },
        // xF 垂线
        {
            x: [xF, xF],
            y: [0, xF],
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {color: 'purple', width: 1, dash: 'dot'},
            showlegend: false,
            hoverinfo: 'none'
        }
    ];

    traces.push(...verticalLines);

    // 添加关键点标注（在x轴上）
    const annotations = [
        {
            x: xD,
            y: 0,
            text: 'xD',
            showarrow: false,
            font: {
                size: 14,
                color: 'red',
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            yshift: -20,
            xanchor: 'center'
        },
        {
            x: xW,
            y: 0,
            text: 'xW',
            showarrow: false,
            font: {
                size: 14,
                color: 'green',
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            yshift: -20,
            xanchor: 'center'
        },
        {
            x: xF,
            y: 0,
            text: 'xF',
            showarrow: false,
            font: {
                size: 14,
                color: 'purple',
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            yshift: -20,
            xanchor: 'center'
        }
    ];

    // 图表布局
    const layout = {
        title: 'McCabe-Thiele 精馏计算图',
        xaxis: {
            title: '液相乙醇摩尔分率 (x)',
            range: [0, 1]
        },
        yaxis: {
            title: '气相乙醇摩尔分率 (y)',
            range: [0, 1]
        },
        showlegend: true,
        legend: {
            x: 0.7,
            y: 0.02,
            bgcolor: 'rgba(255, 255, 255, 0.8)' // 半透明背景
        },
        annotations: annotations,
        font: {
            family: 'Noto Sans SC, Arial, sans-serif',
            size: 14
        },
        width: 600,
        height: 700,
        margin: {
            l: 60,
            r: 30,
            b: 60,
            t: 80,
            pad: 4
        },
        // 禁用点击互动功能
        dragmode: false,
        hovermode: false
    };

    Plotly.newPlot(plotDiv, traces, layout);
}

function plotMcCabeThieleWithSteps(xD, xW, xF, q, R, results, rminData) {
    // 创建带有阶梯的 McCabe-Thiele 图
    const plotDiv = document.getElementById('mccabe-plot');

    // 生成平衡线数据
    const x_values = Array.from({length: 101}, (_, i) => i / 100);
    const y_eq_values = x_values.map(x => equilibriumY(x));

    // 获取操作线交点
    const x_intersect = results.x_intersect;
    const y_intersect = results.y_intersect;

    // 创建图表数据
    const traces = [
        // 对角线 y=x
        {
            x: [0, 1],
            y: [0, 1],
            type: 'scatter',
            mode: 'lines',
            name: 'y = x',
            line: {color: 'black', width: 1}
        },
        // 平衡线
        {
            x: x_values,
            y: y_eq_values,
            type: 'scatter',
            mode: 'lines',
            name: '平衡线',
            line: {color: 'blue', width: 2}
        },
        // VLE 数据点
        {
            x: x_ethanol_data,
            y: y_ethanol_data,
            type: 'scatter',
            mode: 'markers',
            name: 'VLE 数据点',
            marker: {color: 'blue', size: 6, opacity: 0.7}
        },
        // 精馏段操作线
        {
            x: [x_intersect, xD],
            y: [y_intersect, xD],
            type: 'scatter',
            mode: 'lines',
            name: `精馏段操作线 (R=${R.toFixed(2)})`,
            line: {color: 'red', width: 2}
        },
        // 提馏段操作线
        {
            x: [xW, x_intersect],
            y: [xW, y_intersect],
            type: 'scatter',
            mode: 'lines',
            name: '提馏段操作线',
            line: {color: 'green', width: 2}
        },
        // 关键点
        {
            x: [xD, xW, xF, x_intersect],
            y: [xD, xW, xF, y_intersect],
            type: 'scatter',
            mode: 'markers',
            name: '关键点 xD',
            marker: {
                color: ['red', 'green', 'purple', 'black'],
                size: 10,
                symbol: ['circle', 'circle', 'circle', 'circle-open']
            },
            hoverinfo: 'none'
        }
    ];

    // 添加 q 线
    let q_line_trace;
    const TOL = 1e-9;

    if (Math.abs(q - 1.0) < TOL) {
        // 垂直 q 线
        q_line_trace = {
            x: [xF, xF],
            y: [xF, y_intersect],
            type: 'scatter',
            mode: 'lines',
            name: `q 线 (q=${q.toFixed(2)})`,
            line: {color: 'purple', width: 2, dash: 'dash'}
        };
    } else if (Math.abs(q - 0.0) < TOL) {
        // 水平 q 线
        q_line_trace = {
            x: [x_intersect, xF],
            y: [xF, xF],
            type: 'scatter',
            mode: 'lines',
            name: `q 线 (q=${q.toFixed(2)})`,
            line: {color: 'purple', width: 2, dash: 'dash'}
        };
    } else {
        // 斜 q 线
        q_line_trace = {
            x: [xF, x_intersect],
            y: [xF, y_intersect],
            type: 'scatter',
            mode: 'lines',
            name: `q 线 (q=${q.toFixed(2)})`,
            line: {color: 'purple', width: 2, dash: 'dash'}
        };
    }

    traces.push(q_line_trace);

    // 添加夹紧点和 Rmin 线
    if (rminData && !isNaN(rminData.pinchX) && !isNaN(rminData.pinchY)) {
        // 添加夹紧点
        const pinchTrace = {
            x: [rminData.pinchX],
            y: [rminData.pinchY],
            type: 'scatter',
            mode: 'markers',
            name: `夹紧点 (${rminData.pinchType})`,
            marker: {
                color: 'magenta',
                size: 12,
                symbol: 'x'
            }
        };

        // 添加 Rmin 线
        let rminLineLabel;
        if (isFinite(rminData.Rmin)) {
            rminLineLabel = `Rmin 线 (${rminData.Rmin.toFixed(3)})`;
        } else {
            rminLineLabel = 'Rmin = ∞ 线';
        }

        const rminLineTrace = {
            x: [rminData.pinchX, xD],
            y: [rminData.pinchY, xD],
            type: 'scatter',
            mode: 'lines',
            name: rminLineLabel,
            line: {color: 'magenta', width: 1.5, dash: 'dot'}
        };

        traces.push(pinchTrace);
        traces.push(rminLineTrace);
    }

    // 添加阶梯线
    if (results.steps_x && results.steps_y && results.steps_x.length > 1) {
        // 创建阶梯线
        const steps_trace = {
            x: results.steps_x,
            y: results.steps_y,
            type: 'scatter',
            mode: 'lines',
            name: `阶梯线 (总板数: ${results.N_total})`,
            line: {color: 'darkorange', width: 2}
        };

        // 创建阶梯点
        const steps_points_trace = {
            x: results.steps_x,
            y: results.steps_y,
            type: 'scatter',
            mode: 'markers',
            name: '阶梯点',
            marker: {color: 'darkorange', size: 6},
            showlegend: false
        };

        traces.push(steps_trace);
        traces.push(steps_points_trace);

        // 标记进料板位置
        if (results.feed_stage > 0 && results.feed_stage <= results.steps_x.length / 2) {
            // 进料板位置的索引是 feed_stage * 2 - 1 或 feed_stage * 2
            const feed_index = results.feed_stage * 2 - 1;
            if (feed_index < results.steps_x.length) {
                const feed_stage_trace = {
                    x: [results.steps_x[feed_index]],
                    y: [results.steps_y[feed_index]],
                    type: 'scatter',
                    mode: 'markers',
                    name: `进料板 (第 ${results.feed_stage} 板)`,
                    marker: {
                        color: 'purple',
                        size: 12,
                        symbol: 'star'
                    }
                };
                traces.push(feed_stage_trace);
            }
        }
    }

    // 添加从关键点到x轴的垂直虚线
    const verticalLines = [
        // xD 垂线
        {
            x: [xD, xD],
            y: [0, xD],
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {color: 'red', width: 1, dash: 'dot'},
            showlegend: false,
            hoverinfo: 'none'
        },
        // xW 垂线
        {
            x: [xW, xW],
            y: [0, xW],
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {color: 'green', width: 1, dash: 'dot'},
            showlegend: false,
            hoverinfo: 'none'
        },
        // xF 垂线
        {
            x: [xF, xF],
            y: [0, xF],
            type: 'scatter',
            mode: 'lines',
            name: '',
            line: {color: 'purple', width: 1, dash: 'dot'},
            showlegend: false,
            hoverinfo: 'none'
        }
    ];

    traces.push(...verticalLines);

    // 添加关键点标注（在x轴上）
    const annotations = [
        {
            x: xD,
            y: 0,
            text: 'xD',
            showarrow: false,
            font: {
                size: 14,
                color: 'red',
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            yshift: -20,
            xanchor: 'center'
        },
        {
            x: xW,
            y: 0,
            text: 'xW',
            showarrow: false,
            font: {
                size: 14,
                color: 'green',
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            yshift: -20,
            xanchor: 'center'
        },
        {
            x: xF,
            y: 0,
            text: 'xF',
            showarrow: false,
            font: {
                size: 14,
                color: 'purple',
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            yshift: -20,
            xanchor: 'center'
        }
    ];

    // 图表布局
    const layout = {
        title: 'McCabe-Thiele 精馏计算图',
        xaxis: {
            title: '液相乙醇摩尔分率 (x)',
            range: [0, 1]
        },
        yaxis: {
            title: '气相乙醇摩尔分率 (y)',
            range: [0, 1]
        },
        showlegend: true,
        legend: {
            x: 0.7,
            y: 0.02,
            bgcolor: 'rgba(255, 255, 255, 0.8)' // 半透明背景
        },
        annotations: annotations,
        font: {
            family: 'Noto Sans SC, Arial, sans-serif',
            size: 14
        },
        width: 600,
        height: 700,
        margin: {
            l: 60,
            r: 30,
            b: 60,
            t: 80,
            pad: 4
        },
        // 禁用点击互动功能
        dragmode: false,
        hovermode: false
    };

    Plotly.newPlot(plotDiv, traces, layout);
}

// --- 最小回流比计算 ---
function findQLineEquilibriumIntersection(q, xF) {
    // 找到 q 线和平衡线的交点
    const TOL = 1e-9;

    if (Math.abs(q - 1.0) < TOL) {
        return [xF, equilibriumY(xF)];
    }

    // 在 xF 附近搜索交点
    const step = 0.001;
    const searchRange = 0.3;
    let minDiff = Infinity;
    let bestX = NaN;

    for (let x = Math.max(0, xF - searchRange/2); x <= Math.min(1, xF + searchRange/2); x += step) {
        const y_eq = equilibriumY(x);
        const y_q = qLineY(x, q, xF);

        if (!isNaN(y_q)) {
            const diff = Math.abs(y_eq - y_q);
            if (diff < minDiff) {
                minDiff = diff;
                bestX = x;
            }
        }
    }

    if (minDiff < 0.01) {
        return [bestX, equilibriumY(bestX)];
    } else {
        console.warn("未能找到 q 线和平衡线的精确交点");
        return [NaN, NaN];
    }
}

function calculateRminFromPoint(xD, xp, yp) {
    // 根据给定的 xD 和夹紧点 (xp, yp) 计算 Rmin
    const TOL = 1e-9;

    if (yp < xp - TOL) return NaN;
    if (Math.abs(xp - xD) < TOL && Math.abs(yp - xD) < TOL) return Infinity;
    if (Math.abs(yp - xp) < TOL) return Infinity;

    const Rmin = (xD - yp) / (yp - xp);
    return Math.max(0.0, Rmin);
}

function findTangentBasedRmin(xD, xStartSearch, xEndSearch = null) {
    // 在指定范围内查找产生最大 Rmin 的切点
    const TOL = 1e-9;

    // 如果未提供，则将 xEndSearch 默认设为 xD
    if (xEndSearch === null) {
        xEndSearch = xD;
    }

    // 验证搜索范围
    if (xStartSearch >= xEndSearch - TOL) {
        console.warn(`警告: 搜索范围无效 [${xStartSearch.toFixed(4)}, ${xEndSearch.toFixed(4)}]`);
        return [NaN, NaN, NaN];
    }

    if (xStartSearch >= xD - TOL) {
        console.warn(`警告: 起始搜索点 ${xStartSearch.toFixed(4)} 不小于 xD ${xD.toFixed(4)}`);
        return [NaN, NaN, NaN];
    }

    // 确保范围有效
    const xStartSearchClipped = Math.max(0.0, xStartSearch + TOL);
    const xEndSearchClipped = Math.min(xEndSearch, xD - TOL);

    if (xStartSearchClipped >= xEndSearchClipped) {
        console.warn(`警告: 调整后的搜索范围无效 [${xStartSearchClipped.toFixed(4)}, ${xEndSearchClipped.toFixed(4)}]`);
        return [NaN, NaN, NaN];
    }

    // 在范围内创建评估点
    const numPoints = 500;
    const xEval = [];
    const step = (xEndSearchClipped - xStartSearchClipped) / (numPoints - 1);

    for (let i = 0; i < numPoints; i++) {
        xEval.push(xStartSearchClipped + i * step);
    }

    const yEval = xEval.map(x => equilibriumY(x));

    // 查找产生最大 Rmin 的点
    let RminValues = [];
    let validX = [];
    let validY = [];

    for (let i = 0; i < xEval.length; i++) {
        const x_i = xEval[i];
        const y_i = yEval[i];

        // 确保点在 y=x 线之上且 x_i 严格小于 xD
        if (y_i > x_i + TOL && x_i < xD - TOL) {
            const Rmin_i = calculateRminFromPoint(xD, x_i, y_i);

            // 检查 Rmin 是否有效
            if (!isNaN(Rmin_i) && isFinite(Rmin_i) && Rmin_i >= 0) {
                RminValues.push(Rmin_i);
                validX.push(x_i);
                validY.push(y_i);
            }
        }
    }

    if (RminValues.length === 0) {
        console.warn(`警告: 在范围内未找到有效的平衡点计算切线 Rmin`);
        return [NaN, NaN, NaN];
    }

    // 找到最大的 Rmin 及其对应的点
    let maxIndex = 0;
    let maxRmin = RminValues[0];

    for (let i = 1; i < RminValues.length; i++) {
        if (RminValues[i] > maxRmin) {
            maxRmin = RminValues[i];
            maxIndex = i;
        }
    }

    return [RminValues[maxIndex], validX[maxIndex], validY[maxIndex]];
}

function calculateRmin(xD, xF, tF) {
    // 计算最小回流比，返回 Rmin 和夹紧点信息
    console.log(`\n计算 Rmin: xD=${xD.toFixed(4)}, xF=${xF.toFixed(4)}, tF=${tF.toFixed(1)}°C`);
    const TOL = 1e-7;

    let result = {
        Rmin: NaN,
        pinchX: NaN,
        pinchY: NaN,
        pinchType: "未知",
        q: NaN
    };

    try {
        // 计算 q 值
        const q = calculateQValue(xF, tF);
        if (isNaN(q)) throw new Error("q 计算错误");
        result.q = q;

        // 步骤 1: 计算 q 线交点 Pq 和对应的 Rmin_q
        const [xp_q, yp_q] = findQLineEquilibriumIntersection(q, xF);
        const isQPointValid = !isNaN(xp_q) && !isNaN(yp_q);

        let Rmin_q = NaN, Rmin_t = NaN, xt_t = NaN, yt_t = NaN;

        if (isQPointValid) {
            Rmin_q = calculateRminFromPoint(xD, xp_q, yp_q);
            console.log(`  -> q 线交点 Pq: (${xp_q.toFixed(4)}, ${yp_q.toFixed(4)})`);

            if (!isNaN(Rmin_q)) {
                console.log(`  -> 基于 Pq 的 Rmin_q: ${isFinite(Rmin_q) ? Rmin_q.toFixed(4) : '∞'}`);
            } else {
                console.log("  -> 无法从 Pq 计算 Rmin_q");
            }
        } else {
            console.log("  -> 未能找到有效的 q 线/平衡线交点。尝试直接寻找切点。");
        }

        // 步骤 2: 检查在 Pq 右侧是否存在需要更高 Rmin 的切点 Pt
        let isTangentSearchNeeded = false;

        if (isQPointValid && !isNaN(Rmin_q)) {
            // 搜索从 xp_q 开始到 xD 结束的切点
            const [Rmin_t_candidate, xt_t_candidate, yt_t_candidate] = findTangentBasedRmin(xD, xp_q, xD);

            if (!isNaN(Rmin_t_candidate) && isFinite(Rmin_t_candidate) && Rmin_t_candidate >= 0) {
                console.log(`  -> 在 Pq 右侧找到潜在切点 Pt': (${xt_t_candidate.toFixed(4)}, ${yt_t_candidate.toFixed(4)})`);
                console.log(`  -> 基于 Pt' 的 Rmin_t': ${Rmin_t_candidate.toFixed(4)}`);

                // 检查这个右侧切点是否要求比 Rmin_q 更高的回流比
                if (Rmin_t_candidate > Rmin_q + TOL) {
                    isTangentSearchNeeded = true;
                    Rmin_t = Rmin_t_candidate;
                    xt_t = xt_t_candidate;
                    yt_t = yt_t_candidate;
                    console.log(`  -> 条件满足: Rmin_t' (${Rmin_t.toFixed(4)}) > Rmin_q (${Rmin_q.toFixed(4)})。需要寻找最终切点。`);
                } else {
                    console.log(`  -> 条件不满足: Rmin_t' (${Rmin_t_candidate.toFixed(4)}) <= Rmin_q (${Rmin_q.toFixed(4)})。维持 Pq 为控制点。`);
                }
            } else {
                console.log(`  -> 在 Pq 右侧未找到需要有限 Rmin 的有效切点。`);

                // 检查 Rmin_t 是否无限
                const is_t_inf_candidate = !isNaN(Rmin_t_candidate) && !isFinite(Rmin_t_candidate);
                if (is_t_inf_candidate && isFinite(Rmin_q)) {
                    isTangentSearchNeeded = true;
                    Rmin_t = Rmin_t_candidate;
                    xt_t = xt_t_candidate;
                    yt_t = yt_t_candidate;
                    console.log(`  -> 条件满足: Pq 右侧切点需要无限 Rmin，而 Rmin_q 有限。`);
                }
            }
        }

        // 步骤 3: 确定最终 Rmin 和 Pinch Point
        if (isTangentSearchNeeded) {
            // 情况 2: 切点控制
            if (!isNaN(Rmin_t)) {
                result.Rmin = Rmin_t;
                result.pinchX = xt_t;
                result.pinchY = yt_t;
                result.pinchType = "切点";
                console.log(`  -> 最终确定: Rmin 由切点 Pt 控制。`);
            } else {
                throw new Error("逻辑错误：需要切点但未记录有效的切点 Rmin。");
            }
        } else if (isQPointValid && !isNaN(Rmin_q)) {
            // 情况 1: q 线交点控制
            result.Rmin = Rmin_q;
            result.pinchX = xp_q;
            result.pinchY = yp_q;
            result.pinchType = "q-线交点";
            console.log(`  -> 最终确定: Rmin 由 q 线交点 Pq 控制。`);
        } else {
            // 回退策略
            console.log("  -> 回退：Pq 或 Rmin_q 无效，尝试查找全局切点 (xF 到 xD)。");
            const [Rmin_t_global, xt_t_global, yt_t_global] = findTangentBasedRmin(xD, xF, xD);

            if (!isNaN(Rmin_t_global)) {
                result.Rmin = Rmin_t_global;
                result.pinchX = xt_t_global;
                result.pinchY = yt_t_global;
                result.pinchType = "切点 (全局回退)";
                console.log(`  -> 全局切点 Pt_g: (${xt_t_global.toFixed(4)}, ${yt_t_global.toFixed(4)}), Rmin_g: ${isFinite(result.Rmin) ? result.Rmin.toFixed(4) : '∞'}`);
            } else {
                throw new Error("无法确定有效的最小回流比 (Pq 无效且未找到全局切点)。");
            }
        }

        // 最后检查 Rmin 是否为 NaN
        if (isNaN(result.Rmin)) {
            throw new Error("最终 Rmin 计算结果为 NaN。");
        }

        return result;
    } catch (e) {
        console.error(`错误 (calculateRmin): ${e.message}`);
        result.error = e.message;
        return result;
    }
}

// --- 最小板数计算 ---
function calculateNmin(xD, xW) {
    // 计算全回流时的最小板数
    let Nmin = 0;
    let y_curr = parseFloat(xD);
    const xW_fl = parseFloat(xW);
    const MAX_STAGES_NMIN = 100;
    const TOL_NMIN = 1e-7;

    if (!(0.0 <= xD && xD <= 1.0 && 0.0 <= xW_fl && xW_fl <= 1.0 && xW_fl < xD)) {
        return -1;
    }

    while (Nmin < MAX_STAGES_NMIN) {
        const x_eq = equilibriumX(y_curr);
        if (isNaN(x_eq)) {
            return -1;
        }

        if (x_eq <= xW_fl + TOL_NMIN) {
            Nmin += 1;
            break;
        }

        y_curr = x_eq;
        Nmin += 1;
    }

    if (Nmin >= MAX_STAGES_NMIN) {
        console.warn(`警告 (Nmin 计算): 达到最大阶段数 ${MAX_STAGES_NMIN} 未达到 xW。`);
        return -1;
    }

    return Nmin;
}

// --- McCabe-Thiele 阶梯计算 ---
function performMcCabeThiele(xD, xW, xF, q, R) {
    // 执行 McCabe-Thiele 阶梯计算
    const STEP_TOL = 1e-7;
    const results = {
        steps_x: [],
        steps_y: [],
        N_total: 0,
        feed_stage: -1,
        error: null,
        x_intersect: NaN,
        y_intersect: NaN,
        stripping_slope: NaN,
        stripping_intercept: NaN
    };

    try {
        // 验证输入
        if (!(0.0 <= xD && xD <= 1.0)) throw new Error("塔顶组成 xD 必须在 [0, 1] 范围内");
        if (!(0.0 <= xW && xW <= 1.0)) throw new Error("塔底组成 xW 必须在 [0, 1] 范围内");
        if (!(0.0 <= xF && xF <= 1.0)) throw new Error("进料组成 xF 必须在 [0, 1] 范围内");
        if (!(xW < xD)) throw new Error("塔顶组成 xD 必须大于塔底组成 xW");
        if (R <= 0) throw new Error("回流比 R 必须大于 0");

        // 计算操作线交点
        const [x_intersect, y_intersect] = findIntersection(R, xD, q, xF);
        results.x_intersect = x_intersect;
        results.y_intersect = y_intersect;

        if (isNaN(x_intersect)) {
            throw new Error("无法计算操作线交点。请检查 R 和 q 值。");
        }

        if (y_intersect < x_intersect - 1e-6) {
            throw new Error(`操作线交点 (${x_intersect.toFixed(4)}, ${y_intersect.toFixed(4)}) 低于 y=x 线。无法操作 (可能 R < Rmin)。`);
        }

        // 计算提馏段操作线参数
        const [strip_slope, strip_intercept] = strippingOpLineCoeffs(x_intersect, y_intersect, xW);
        results.stripping_slope = strip_slope;
        results.stripping_intercept = strip_intercept;

        if (isNaN(strip_slope)) {
            const y_eq_at_xW = equilibriumY(xW);
            if (!isNaN(y_eq_at_xW) && y_intersect < y_eq_at_xW - 1e-6) {
                throw new Error(`无法达到目标 xW=${xW.toFixed(4)}。操作线交点 y=${y_intersect.toFixed(4)} 低于 xW 对应的平衡点 y=${y_eq_at_xW.toFixed(4)}。`);
            } else {
                throw new Error("无法确定提馏段操作线（交点可能接近 (xW, xW) 或接近垂直）。");
            }
        }

        // 执行阶梯计算
        let x_curr = xD, y_curr = xD;
        let stage_count = 0;
        const steps_x = [xD], steps_y = [xD];
        const MAX_STAGES = 100;
        let feed_stage_number = -1;

        while (stage_count < MAX_STAGES) {
            const current_stage_number = stage_count + 1;

            // 从操作线到平衡线（垂直线）
            const x_eq = equilibriumX(y_curr);
            if (isNaN(x_eq)) {
                throw new Error(`第 ${current_stage_number} 板平衡液相计算失败 (y=${y_curr.toFixed(4)})`);
            }

            const x_eq_clipped = Math.max(0.0, x_eq);
            steps_x.push(x_eq_clipped);
            steps_y.push(y_curr);

            // 检查是否达到塔底组成
            if (x_eq_clipped <= xW + STEP_TOL) {
                stage_count += 1;
                break;
            }

            // 确定使用哪条操作线
            const use_rectifying_line = (x_eq_clipped >= x_intersect - STEP_TOL);
            if (!use_rectifying_line && feed_stage_number === -1) {
                feed_stage_number = current_stage_number;
            }

            // 从平衡线到操作线（水平线）
            let y_op;
            if (use_rectifying_line) {
                y_op = rectifyingOpLine(x_eq_clipped, R, xD);
                if (isNaN(y_op)) {
                    throw new Error(`第 ${current_stage_number} 板精馏段操作线计算失败 (x=${x_eq_clipped.toFixed(4)})`);
                }
            } else {
                y_op = strip_slope * x_eq_clipped + strip_intercept;
                if (isNaN(y_op)) {
                    throw new Error(`第 ${current_stage_number} 板提馏段操作线计算失败 (x=${x_eq_clipped.toFixed(4)})`);
                }

                // 如果操作线低于对角线，强制到对角线上
                if (y_op < x_eq_clipped - STEP_TOL) {
                    y_op = x_eq_clipped;
                }
            }

            const y_op_clipped = Math.min(1.0, y_op);
            steps_x.push(x_eq_clipped);
            steps_y.push(y_op_clipped);

            x_curr = x_eq_clipped;
            y_curr = y_op_clipped;
            stage_count += 1;
        }

        // 保存结果
        results.steps_x = steps_x;
        results.steps_y = steps_y;
        results.N_total = stage_count;

        // 检查是否达到最大迭代次数
        if (stage_count === MAX_STAGES && x_curr > xW + STEP_TOL) {
            results.error = `达到最大迭代次数 (${MAX_STAGES}) 未达到 xW (停止于 x=${x_curr.toFixed(4)})`;
        }

        // 确定进料板位置
        if (feed_stage_number !== -1) {
            results.feed_stage = feed_stage_number;
        } else {
            results.feed_stage = results.N_total > 0 ? results.N_total : 0;
        }

        // 确保进料板位置有效
        if (results.N_total > 0) {
            results.feed_stage = Math.max(1, Math.min(results.feed_stage, results.N_total));
        } else {
            results.feed_stage = 0;
        }

        return results;
    } catch (e) {
        results.error = e.message;
        console.error("McCabe-Thiele Error:", e.message);
        return results;
    }
}

// --- 页面加载时初始化 ---
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-btn');

    calculateBtn.addEventListener('click', function() {
        // 获取输入值
        const xD = parseFloat(document.getElementById('xD').value);
        const xW = parseFloat(document.getElementById('xW').value);
        const xF = parseFloat(document.getElementById('xF').value);
        const tF = parseFloat(document.getElementById('tF').value);
        const R = parseFloat(document.getElementById('R').value);

        // 验证输入
        if (isNaN(xD) || isNaN(xW) || isNaN(xF) || isNaN(tF) || isNaN(R)) {
            alert('请输入所有必要的参数');
            return;
        }

        if (xD <= xW) {
            alert('塔顶组成 xD 必须大于塔底组成 xW');
            return;
        }

        if (R <= 0) {
            alert('回流比 R 必须大于 0');
            return;
        }

        // 计算 q 值
        const q = calculateQValue(xF, tF);
        if (isNaN(q)) {
            alert('计算 q 值失败，请检查进料组成和温度');
            return;
        }
        document.getElementById('q-value').textContent = q.toFixed(3);

        // 计算 Rmin 和夹紧点
        const rminData = calculateRmin(xD, xF, tF);
        if (!isNaN(rminData.Rmin)) {
            if (isFinite(rminData.Rmin)) {
                document.getElementById('rmin-value').textContent = rminData.Rmin.toFixed(3);
            } else {
                document.getElementById('rmin-value').textContent = "∞";
            }
        } else {
            document.getElementById('rmin-value').textContent = "计算错误";
        }

        // 计算 Nmin
        const Nmin = calculateNmin(xD, xW);
        if (Nmin >= 0) {
            document.getElementById('nmin-value').textContent = Nmin.toString();
        } else {
            document.getElementById('nmin-value').textContent = "计算错误";
        }

        // 执行 McCabe-Thiele 计算
        const results = performMcCabeThiele(xD, xW, xF, q, R);

        if (results.error) {
            alert('McCabe-Thiele 计算错误: ' + results.error);
            document.getElementById('n-total-value').textContent = "错误";
            document.getElementById('feed-stage-value').textContent = "错误";
            document.getElementById('n-rectifying-value').textContent = "错误";
            document.getElementById('n-stripping-value').textContent = "错误";
            document.getElementById('r-value').textContent = "错误";
            document.getElementById('r-ratio-value').textContent = "错误";
        } else {
            const n_total = results.N_total;
            const feed_stage = results.feed_stage;
            const n_rectifying = feed_stage > 0 ? feed_stage - 1 : 0;
            const n_stripping = n_total - feed_stage + 1; // 进料板算在提馏段

            // 显示计算结果
            document.getElementById('n-total-value').textContent = n_total.toString();
            document.getElementById('feed-stage-value').textContent = feed_stage.toString();
            document.getElementById('n-rectifying-value').textContent = n_rectifying.toString();
            document.getElementById('n-stripping-value').textContent = n_stripping.toString();

            // 显示回流比相关信息
            document.getElementById('r-value').textContent = R.toFixed(2);

            // 计算回流比与最小回流比的比值
            if (!isNaN(rminData.Rmin) && isFinite(rminData.Rmin) && rminData.Rmin > 0) {
                const r_ratio = R / rminData.Rmin;
                document.getElementById('r-ratio-value').textContent = r_ratio.toFixed(2);
            } else {
                document.getElementById('r-ratio-value').textContent = "N/A";
            }

            // 绘制 McCabe-Thiele 图，包括阶梯和夹紧点
            plotMcCabeThieleWithSteps(xD, xW, xF, q, R, results, rminData);
        }
    });
});
