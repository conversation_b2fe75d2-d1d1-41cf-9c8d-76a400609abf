<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源下载</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .download-item {
            border-left: 4px solid #0072B2;
            padding: 1rem 1.2rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            border-radius: 0 0.5rem 0.5rem 0;
            background-color: rgba(240, 249, 255, 0.5);
        }

        .download-item:hover {
            border-left-color: #D55E00;
            background-color: rgba(240, 249, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .download-btn {
            background-color: #0072B2;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .download-btn:hover {
            background-color: #D55E00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .file-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
        }

        .file-icon.pdf {
            background-color: #dc2626;
        }

        .file-icon.doc {
            background-color: #2563eb;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link">首页</a>
                <a href="profile.html" class="nav-link">个人</a>
                <a href="research.html" class="nav-link">科研</a>
                <a href="teaching.html" class="nav-link">教学</a>
                <a href="members.html" class="nav-link">成员</a>
                <a href="tool.html" class="nav-link">工具</a>
                <a href="downloads.html" class="nav-link active">下载</a>
                <a href="index-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <!-- 资源下载内容 -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                资源下载
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <!-- 流体力学与传热实验 -->
        <div class="card p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">流体力学与传热实验</h2>
            <p class="mb-6">提供流体力学与传热相关的实验指导书和讲义，包括流体流动、传热等核心单元操作的实验资料。这些资料将帮助学生更好地理解流体力学和传热的基本概念和实验操作技能。</p>

            <div class="download-item">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="file-icon doc" style="background-color: #6b7280;">待定</div>
                        <div>
                            <p class="font-semibold">流体力学与传热实验资料</p>
                            <p class="text-sm text-gray-600">实验指导书和相关资料（待提供）</p>
                        </div>
                    </div>
                    <button class="download-btn" style="background-color: #6b7280; cursor: not-allowed;" disabled>
                        <i class="fas fa-clock"></i>
                        待提供
                    </button>
                </div>
            </div>
        </div>

        <!-- 传质与分离工程 -->
        <div class="card p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">传质与分离工程实验</h2>
            <p class="mb-6">提供传质与分离工程相关的实验指导书和讲义，包括精馏、吸收、干燥等核心单元操作的实验资料。这些资料将帮助学生更好地理解传质分离过程的基本概念和实验操作技能。</p>

            <div class="download-item">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="file-icon" style="background-color: #dc2626; color: white; font-size: 0.75rem;">RAR</div>
                        <div>
                            <p class="font-semibold">传质与分离工程实验</p>
                            <p class="text-sm text-gray-600">包含精馏、吸收、干燥等实验的完整资料包</p>
                        </div>
                    </div>
                    <a href="downloads/传质与分离工程实验.rar" class="download-btn" download>
                        <i class="fas fa-download"></i>
                        下载
                    </a>
                </div>
            </div>
        </div>

        <!-- 化工原理课程设计 -->
        <div class="card p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">化工原理课程设计</h2>
            <p class="mb-6">提供化工原理课程设计相关的设计指导书、计算数据和参考资料，帮助学生完成课程设计任务，掌握化工工艺设计的基本方法和技能。</p>

            <div class="download-item">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="file-icon" style="background-color: #dc2626; color: white; font-size: 0.75rem;">RAR</div>
                        <div>
                            <p class="font-semibold">化工原理课程设计数据</p>
                            <p class="text-sm text-gray-600">包含设计指导书、计算数据和参考资料的完整资料包</p>
                        </div>
                    </div>
                    <a href="downloads/化工原理课程设计数据.rar" class="download-btn" download>
                        <i class="fas fa-download"></i>
                        下载
                    </a>
                </div>
            </div>
        </div>

        <!-- Python编程与机器学习 -->
        <div class="card p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-[#0072B2]">Python编程与机器学习</h2>
            <p class="mb-6">提供Python编程基础和机器学习在化工领域应用的相关资料，包括编程教程、算法实现和实际案例，帮助学生掌握现代数据分析和预测建模技能。</p>

            <div class="download-item">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="file-icon" style="background-color: #dc2626; color: white; font-size: 0.75rem;">RAR</div>
                        <div>
                            <p class="font-semibold">Python编程与机器学习</p>
                            <p class="text-sm text-gray-600">包含Python基础教程、机器学习算法和化工应用案例的完整资料包</p>
                        </div>
                    </div>
                    <a href="downloads/Python编程与机器学习.rar" class="download-btn" download>
                        <i class="fas fa-download"></i>
                        下载
                    </a>
                </div>
            </div>
        </div>

        <div class="card p-6 mb-8">
            <h3 class="text-2xl font-semibold mb-4 text-[#0072B2]">使用说明</h3>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="gradient-bg p-4 rounded-lg shadow-md">
                    <h4 class="font-semibold mb-2 text-center">下载须知</h4>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>所有资料仅供教学和学术研究使用</li>
                        <li>点击下载按钮即可获取相应文件</li>
                        <li>建议使用最新版本的Office或PDF阅读器打开</li>
                        <li>如有问题请联系课程教师</li>
                    </ul>
                </div>

                <div class="gradient-bg p-4 rounded-lg shadow-md">
                    <h4 class="font-semibold mb-2 text-center">文件说明</h4>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>DOC格式：报告模板，包含完成内容</li>
                        <li>PDF格式：实验讲义，包含理论知识和原理</li>
                        <li>文件大小适中，下载速度较快</li>
                        <li>支持在线预览和本地下载</li>
                    </ul>
                </div>
            </div>

            <div class="mt-6 text-center">
                <p class="mb-4">如需更多实验资料或有任何疑问，请联系课程教师。</p>
                <span class="download-btn">
                    <i class="fas fa-envelope mr-2"></i> Email：<EMAIL>
                </span>
            </div>
        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // 基本的页面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
            console.log('下载页面加载完成');

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
