<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小组成员</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }



        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.4), transparent);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.02), rgba(0, 102, 153, 0.02));
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .card:hover::before {
            opacity: 1;
            height: 3px;
        }

        .card:hover::after {
            opacity: 1;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #e6f7ff;
        }

        .btn {
            background-color: #0072B2;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
            text-decoration: none;
            text-align: center;
            border: none;
            outline: none;
        }

        .btn:hover {
            background-color: #D55E00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link">首页</a>
                <a href="profile.html" class="nav-link">个人</a>
                <a href="research.html" class="nav-link">科研</a>
                <a href="teaching.html" class="nav-link">教学</a>
                <a href="members.html" class="nav-link active">成员</a>
                <a href="tool.html" class="nav-link">工具</a>
                <a href="downloads.html" class="nav-link">下载</a>
                <a href="members-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <!-- 小组成员内容 -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-16">
            <h1 class="text-3xl md:text-4xl font-bold text-[#006699] mb-6 leading-tight tracking-tight">
                小组成员
            </h1>
            <div class="w-24 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-6 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="mb-6 text-lg text-center">
            <p>2026级硕士研究生指标已用完！---2025.9.25</p>
        </div>

        <!-- 在读研究生 -->
        <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-6 text-[#0072B2] text-center">在读研究生（11人）</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

                <!-- 詹佳敏 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/詹佳敏.jpg" alt="詹佳敏照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=詹佳敏'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">詹佳敏</h3>
                    <p class="text-lg text-gray-600 mb-2">2023级硕士生</p>
                    <p class="text-lg text-gray-500">COF人工酶</p>
                </div>

                <!-- 卢浩盛 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/卢浩盛.jpg" alt="卢浩盛照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=卢浩盛'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">卢浩盛</h3>
                    <p class="text-lg text-gray-600 mb-2">2023级硕士生（士兵计划）</p>
                    <p class="text-lg text-gray-500">COF人工酶</p>
                </div>

                <!-- 卢颐康 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/卢颐康.jpg" alt="卢颐康照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=卢颐康'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">卢颐康</h3>
                    <p class="text-lg text-gray-600 mb-2">2023级硕士生</p>
                    <p class="text-lg text-gray-500">COF固定化酶</p>
                </div>

                <!-- 戴华杰 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/戴华杰.jpg" alt="戴华杰照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=戴华杰'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">戴华杰</h3>
                    <p class="text-lg text-gray-600 mb-2">2024级硕士生</p>
                    <p class="text-lg text-gray-500">COF人工酶</p>
                </div>

                <!-- 潘杨 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/潘杨.jpg" alt="潘杨照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=潘杨'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">潘杨</h3>
                    <p class="text-lg text-gray-600 mb-2">2024级硕士生</p>
                    <p class="text-lg text-gray-500">COF固定化酶</p>
                </div>

                <!-- 麦伟健 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/麦伟健.jpg" alt="麦伟健照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=麦伟健'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">麦伟健</h3>
                    <p class="text-lg text-gray-600 mb-2">2024级硕士生</p>
                    <p class="text-lg text-gray-500">COF人工酶</p>
                </div>

                <!-- 曾惠梅 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/曾惠梅.jpg" alt="曾惠梅照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=曾惠梅'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">曾惠梅</h3>
                    <p class="text-lg text-gray-600 mb-2">2025级硕士</p>
                    <p class="text-lg text-gray-500">研究方向：待定</p>
                </div>

                <!-- 陶长成 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/陶长成.jpg" alt="陶长成照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=陶长成'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">陶长成</h3>
                    <p class="text-lg text-gray-600 mb-2">2025级硕士生</p>
                    <p class="text-lg text-gray-500">研究方向：待定</p>
                </div>

                <!-- 尹正 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/尹正.jpg?v=1.1.1" alt="尹正照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=尹正'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">尹正</h3>
                    <p class="text-lg text-gray-600 mb-2">2026级硕士生</p>
                    <p class="text-lg text-gray-500">研究方向：待定</p>
                </div>

                <!-- 黄嘉怡 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/黄嘉怡.jpg?v=1.1.1" alt="黄嘉怡照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=黄嘉怡'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">黄嘉怡</h3>
                    <p class="text-lg text-gray-600 mb-2">2026级硕士生</p>
                    <p class="text-lg text-gray-500">研究方向：待定</p>
                </div>

                <!-- 肖如琪 -->
                <div class="card p-6 text-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                        <img src="assets/images/members/肖如琪.jpg?v=1.1.1" alt="肖如琪照片" class="w-full h-full object-cover" onerror="this.src='https://via.placeholder.com/128x128?text=肖如琪'">
                    </div>
                    <h3 class="text-lg font-semibold mb-2 text-[#006699]">肖如琪</h3>
                    <p class="text-lg text-gray-600 mb-2">2026级硕士生</p>
                    <p class="text-lg text-gray-500">研究方向：待定</p>
                </div>
            </div>
        </div>

        <!-- 已毕业研究生 -->
        <div class="mb-12">
            <h2 class="text-2xl font-semibold mb-6 text-[#0072B2] text-center">已毕业研究生（16人）</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- 蒋文智 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">蒋文智</h3>
                </div>

                <!-- 李奇峰 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">李奇峰</h3>
                </div>

                <!-- 刘健康 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">刘健康</h3>
                </div>

                <!-- 师伦伦 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">师伦伦</h3>
                </div>

                <!-- 李良卫 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">李良卫(士兵计划)</h3>
                </div>

                <!-- 张迎春 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">张迎春</h3>
                </div>

                <!-- 颜才华 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">颜才华</h3>
                </div>

                <!-- 谭海城 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">谭海城</h3>
                </div>

                <!-- 王媛媛 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">王媛媛</h3>
                </div>

                <!-- 王鹏 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">王鹏</h3>
                </div>

                <!-- 谢远祥 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">谢远祥</h3>
                </div>

                <!-- 王亚琳 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">王亚琳</h3>
                </div>

                <!-- 钟雪 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">钟雪</h3>
                </div>

                <!-- 夏欢 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">夏欢</h3>
                </div>

                <!-- 庞嘉凤 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">庞嘉凤</h3>
                </div>

                <!-- 李少敏 -->
                <div class="card p-4 text-center">
                    <h3 class="text-lg font-semibold text-[#006699]">李少敏</h3>
                </div>
            </div>
        </div>

        <!-- 加入我们 -->
        <div class="card p-8 mb-10">
            <div class="text-center">
                <h3 class="text-xl font-semibold mb-4 text-[#0072B2]">加入我们</h3>

                <span class="btn">
                    <i class="fas fa-envelope mr-2"></i>联系我们：<EMAIL>
                </span>
            </div>
        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // 基本的页面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
            console.log('成员页面加载完成');

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>