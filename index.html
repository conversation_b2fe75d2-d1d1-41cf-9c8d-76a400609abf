<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <!-- 页面版本: v1.1.1 - 更新时间: 2025-06-03 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #0072B2;
            --primary-orange: #D55E00;
            --dark-blue: #006699;
            --light-blue: #f0f9ff;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 16px;
            margin: 0;
            padding: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 全局滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--spacing-md) 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-2xl);
            border-bottom-color: rgba(0, 114, 178, 0.1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.2), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .header:hover::before {
            opacity: 1;
        }

        .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: -0.01em;
        }

        .nav-link:hover {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.06);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.1), rgba(0, 102, 153, 0.1));
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--dark-blue));
            border-radius: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
        }

        .nav-link.active {
            color: var(--primary-blue);
            background: rgba(0, 114, 178, 0.1);
            font-weight: 600;
        }





        .hero-section {
            min-height: calc(100vh - 80px);
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
            position: relative;
            overflow: hidden;
        }

        .tech-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect fill="none" width="100" height="100"/><rect fill="none" stroke="%23006699" stroke-width="0.5" stroke-opacity="0.1" x="10" y="10" width="80" height="80"/><line x1="0" y1="0" x2="100" y2="100" stroke="%23006699" stroke-width="0.5" stroke-opacity="0.1"/><line x1="100" y1="0" x2="0" y2="100" stroke="%23006699" stroke-width="0.5" stroke-opacity="0.1"/></svg>');
            opacity: 0.3;
            pointer-events: none; /* 添加这一行，使元素不拦截鼠标事件 */
        }

        .data-flow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 114, 178, 0.05) 50%, transparent 100%);
            animation: dataFlow 8s linear infinite;
            pointer-events: none; /* 添加这一行，使元素不拦截鼠标事件 */
        }

        @keyframes dataFlow {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.8);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 114, 178, 0.4), transparent);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.02), rgba(0, 102, 153, 0.02));
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .card:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: var(--shadow-2xl);
            border-color: rgba(0, 114, 178, 0.2);
        }

        .card:hover::before {
            opacity: 1;
            height: 3px;
        }

        .card:hover::after {
            opacity: 1;
        }

        .btn {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            color: white;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: 14px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            text-align: center;
            border: none;
            outline: none;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            letter-spacing: -0.01em;
            min-height: 48px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
            transition: left 0.6s ease;
        }

        .btn::after {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .btn:hover {
            background: linear-gradient(135deg, var(--primary-orange), #c44a00);
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            color: white;
            text-decoration: none;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover::after {
            opacity: 1;
        }

        .btn:active {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .research-tag {
            display: inline-block;
            background: linear-gradient(135deg, rgba(240, 249, 255, 0.8), rgba(219, 234, 254, 0.8));
            color: var(--primary-blue);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 16px;
            margin-right: var(--spacing-xs);
            margin-bottom: var(--spacing-xs);
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(0, 114, 178, 0.15);
            cursor: pointer;
            backdrop-filter: blur(8px);
            letter-spacing: -0.01em;
        }

        .research-tag:hover {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            color: white;
            transform: translateY(-3px) scale(1.05);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-blue);
        }

        .resource-item {
            border-radius: var(--border-radius);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: var(--spacing-sm);
            position: relative;
            overflow: hidden;
        }

        .resource-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 114, 178, 0.03), rgba(0, 102, 153, 0.03));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .resource-item:hover {
            background: linear-gradient(135deg, rgba(240, 249, 255, 0.8), rgba(240, 249, 255, 0.6));
            transform: translateX(6px);
            box-shadow: var(--shadow-md);
        }

        .resource-item:hover::before {
            opacity: 1;
        }



        /* 高级感标题样式 */
        .elegant-title {
            position: relative;
            display: inline-block;
            font-family: 'Noto Serif SC', 'Source Han Serif SC', 'STSong', serif;
            font-weight: 700;
            background: linear-gradient(135deg, #006699 0%, #0072B2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 0.1em;
            line-height: 1.4;
            transition: all 0.4s ease;
        }

        /* 简化的光泽效果 */
        @keyframes textShimmer {
            0% {
                background-position: -100% center;
            }
            100% {
                background-position: 100% center;
            }
        }

        .elegant-title.shimmer {
            background: linear-gradient(
                90deg,
                #D55E00 0%,
                #ff6b35 40%,
                #ff8c5a 50%,
                #ff6b35 60%,
                #D55E00 100%
            );
            background-size: 150% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShimmer 6s ease-in-out infinite;
        }

        /* 团队信息样式 - 与elegant-title相似但稍小 */
        .team-subtitle {
            position: relative;
            display: block;
            font-family: 'Noto Serif SC', 'Source Han Serif SC', 'STSong', serif;
            font-weight: 600;
            font-size: 1.5rem;
            background: linear-gradient(
                90deg,
                #006699 0%,
                #0072B2 40%,
                #4A9FD9 50%,
                #0072B2 60%,
                #006699 100%
            );
            background-size: 150% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 0.08em;
            line-height: 1.4;
            animation: textShimmer 6s ease-in-out infinite;
            margin-top: 0.5rem;
            text-align: center;
        }

        @media (min-width: 768px) {
            .team-subtitle {
                font-size: 1.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center">
            </div>
            <div class="flex space-x-6">
                <a href="index.html" class="nav-link active">首页</a>
                <a href="profile.html" class="nav-link">个人</a>
                <a href="research.html" class="nav-link">科研</a>
                <a href="teaching.html" class="nav-link">教学</a>
                <a href="members.html" class="nav-link">成员</a>
                <a href="tool.html" class="nav-link">工具</a>
                <a href="downloads.html" class="nav-link">下载</a>
                <a href="index-en.html" class="nav-link">
                    <i class="fas fa-globe mr-2"></i>English
                </a>
            </div>
        </div>
    </header>

    <!-- 主页内容 -->
    <main class="container mx-auto px-4 py-12">
        <div class="text-center mb-20">
            <h1 class="text-4xl md:text-5xl font-bold mb-8 leading-tight tracking-tight elegant-title shimmer">
                格物致知·臻于至善
            </h1>
            <div class="team-subtitle mb-6">
                邱学青团队　李致贤课题组
            </div>
            <p class="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
                专注于生物与仿生催化研究，致力于推动生物质资源高效转化
            </p>
            <div class="w-32 h-1.5 bg-gradient-to-r from-[#D55E00] via-[#ff6b35] to-[#D55E00] mx-auto mt-8 rounded-full shadow-lg"></div>
            <div class="flex justify-center mt-6 space-x-2">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <div class="grid md:grid-cols-4 gap-8 mt-16">
            <!-- 个人介绍 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-user-tie text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">个人介绍</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">个人简介与学术背景</p>
                <a href="profile.html" class="btn mt-auto">查看详情 &rarr;</a>
            </div>

            <!-- 科研方向 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-flask text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">科研方向</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">木质素高值化利用与生物仿生催化</p>
                <a href="research.html" class="btn mt-auto">探索研究 &rarr;</a>
            </div>

            <!-- 教学工作 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-chalkboard-teacher text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">教学工作</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">AI赋能化工原理课程教学新模式</p>
                <a href="teaching.html" class="btn mt-auto">了解更多 &rarr;</a>
            </div>

            <!-- 小组成员 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-green-50 via-green-100 to-green-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-users text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">小组成员</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">查看和更新招生信息</p>
                <a href="members.html" class="btn mt-auto">加入我们 &rarr;</a>
            </div>
            </div>

        <div class="grid md:grid-cols-3 gap-8 mt-12">
            <!-- 计算工具 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-cyan-50 via-cyan-100 to-cyan-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-calculator text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">计算工具</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">精馏、吸收和干燥过程的程序计算</p>
                <a href="tool.html" class="btn mt-auto">开始计算 &rarr;</a>
            </div>

            <!-- 微信公众号 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-32 h-32 bg-gradient-to-br from-green-50 via-green-100 to-green-200 rounded-3xl flex items-center justify-center mb-6 group-hover:scale-105 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <img src="assets/images/微信公众号.jpg" alt="微信公众号二维码" class="w-24 h-24 rounded-2xl relative z-10">
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">微信公众号</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">请关注"生物质与绿色催化"</p>
            </div>

            <!-- 资源下载 -->
            <div class="card p-8 flex flex-col items-center group">
                <div class="w-24 h-24 bg-gradient-to-br from-indigo-50 via-indigo-100 to-indigo-200 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 transition-all duration-500 shadow-xl group-hover:shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"></div>
                    <i class="fas fa-download text-4xl text-[#0072B2] group-hover:text-[#D55E00] transition-all duration-500 relative z-10"></i>
                </div>
                <h2 class="text-xl font-bold mb-3 text-center">资源下载</h2>
                <p class="text-gray-600 mb-6 text-center leading-relaxed">化工原理课程相关资源，包括实验指导和讲义</p>
                <a href="downloads.html" class="btn mt-auto">下载资源 &rarr;</a>
            </div>


        </div>
    </main>

    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 mt-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="mb-4">
                <span class="text-2xl font-bold">
                    <span class="text-[#D55E00]">COF</span><span class="text-[#0072B2]">zyme</span>
                </span>
            </div>
            <p class="text-gray-300 text-lg">© 2025 COFzyme格致. All Rights Reserved. 版权所有。</p>
            <p class="text-gray-300 text-lg">本网站内容仅用于科研与教学分享，无任何商业用途。粤ICP备2025423918号。</p>
            <div class="mt-6 flex justify-center space-x-6">
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-[#D55E00] rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                <div class="w-2 h-2 bg-[#0072B2] rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>
    </footer>

    <script>
        // 基本的页面功能
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
            console.log('页面加载完成');

            // 添加版本号到页面，防止缓存问题
            const version = '1.1.1-' + Date.now();
            document.documentElement.setAttribute('data-version', version);
        });
    </script>
</body>
</html>
